import React from 'react';
import {
  Card,
  Alert,
  Descriptions,
  Tag,
  Progress,
  Collapse,
  Typography,
  Space,
  Divider,
} from 'antd';
import {
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import type { DuplicateDetectionResult } from '../types';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface DuplicateDetectionResultProps {
  result: DuplicateDetectionResult;
  documentName?: string;
}

const DuplicateDetectionResult: React.FC<DuplicateDetectionResultProps> = ({
  result,
  documentName,
}) => {
  const similarityPercent = Math.round(result.similarity_score * 100);
  
  // 获取相似度等级和颜色
  const getSimilarityLevel = (score: number) => {
    if (score >= 0.8) return { level: '极高', color: '#ff4d4f' };
    if (score >= 0.6) return { level: '高', color: '#ff7a45' };
    if (score >= 0.45) return { level: '中等', color: '#faad14' };
    if (score >= 0.3) return { level: '较低', color: '#52c41a' };
    return { level: '低', color: '#1890ff' };
  };

  const { level, color } = getSimilarityLevel(result.similarity_score);

  return (
    <Card title="重复检测结果" style={{ marginTop: 16 }}>
      {/* 总体结果 */}
      <Alert
        message={
          result.is_duplicate
            ? `检测到重复内容 - 相似度: ${similarityPercent}%`
            : `未检测到重复内容 - 相似度: ${similarityPercent}%`
        }
        description={
          result.is_duplicate
            ? `该文档与已存在的文档"${result.duplicate_document_name}"存在较高相似度，建议检查是否为重复建设。`
            : '该文档内容相对独特，未发现明显的重复内容。'
        }
        type={result.is_duplicate ? 'warning' : 'success'}
        icon={
          result.is_duplicate ? (
            <ExclamationCircleOutlined />
          ) : (
            <CheckCircleOutlined />
          )
        }
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* 相似度详情 */}
      <Descriptions title="检测详情" bordered column={2}>
        <Descriptions.Item label="文档名称">
          {documentName || '当前文档'}
        </Descriptions.Item>
        <Descriptions.Item label="检测时间">
          {result.detection_time
            ? new Date(result.detection_time).toLocaleString()
            : '未知'}
        </Descriptions.Item>
        <Descriptions.Item label="总体相似度">
          <Space>
            <Progress
              percent={similarityPercent}
              size="small"
              strokeColor={color}
              style={{ width: 100 }}
            />
            <Tag color={color}>{level}</Tag>
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="是否重复">
          <Tag color={result.is_duplicate ? 'red' : 'green'}>
            {result.is_duplicate ? '是' : '否'}
          </Tag>
        </Descriptions.Item>
        {result.duplicate_document_name && (
          <Descriptions.Item label="相似文档" span={2}>
            <Text strong>{result.duplicate_document_name}</Text>
          </Descriptions.Item>
        )}
      </Descriptions>

      {/* 详细对比内容 */}
      {result.similar_content && result.similar_content.length > 0 && (
        <div style={{ marginTop: 24 }}>
          <Title level={4}>
            <WarningOutlined style={{ color: '#faad14', marginRight: 8 }} />
            相似内容对比
          </Title>
          <Collapse ghost>
            {result.similar_content.map((item, index) => {
              const itemPercent = Math.round(item.similarity_score * 100);
              const itemLevel = getSimilarityLevel(item.similarity_score);
              
              return (
                <Panel
                  header={
                    <Space>
                      <Text strong>{item.type}</Text>
                      <Tag color={itemLevel.color}>
                        相似度: {itemPercent}%
                      </Tag>
                    </Space>
                  }
                  key={index}
                >
                  <div style={{ padding: '0 16px' }}>
                    <div style={{ marginBottom: 16 }}>
                      <Text type="secondary">当前文档内容：</Text>
                      <Paragraph
                        style={{
                          background: '#f6f8fa',
                          padding: 12,
                          borderRadius: 4,
                          marginTop: 8,
                        }}
                      >
                        {item.current_content}
                      </Paragraph>
                    </div>
                    
                    <Divider style={{ margin: '12px 0' }} />
                    
                    <div>
                      <Text type="secondary">相似文档内容：</Text>
                      <Paragraph
                        style={{
                          background: '#fff2e8',
                          padding: 12,
                          borderRadius: 4,
                          marginTop: 8,
                        }}
                      >
                        {item.similar_content}
                      </Paragraph>
                    </div>
                  </div>
                </Panel>
              );
            })}
          </Collapse>
        </div>
      )}

      {/* 建议 */}
      {result.is_duplicate && (
        <Alert
          message="处理建议"
          description={
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              <li>检查两个文档的功能需求是否存在重叠</li>
              <li>考虑合并相似的功能模块，避免重复建设</li>
              <li>如确需独立建设，请明确差异化需求</li>
              <li>建议与相关团队沟通，确认项目范围</li>
            </ul>
          }
          type="info"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}
    </Card>
  );
};

export default DuplicateDetectionResult;