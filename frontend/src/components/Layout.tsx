import React from 'react';
import { Layout as AntLayout, Menu, Button, Dropdown, Space, Typography } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  FileTextOutlined,
  MessageOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BulbOutlined,
  UserOutlined,
  TableOutlined,
  DiffOutlined,
  FolderOutlined,
} from '@ant-design/icons';
import { useAppStore } from '../utils/store';
import type { MenuProps } from 'antd';

const { Header, Sider, Content } = AntLayout;
const { Title } = Typography;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { sidebarCollapsed, theme, setSidebarCollapsed, setTheme } = useAppStore();

  // 菜单项配置
  const menuItems = [
    {
      key: '/projects',
      icon: <FolderOutlined />,
      label: '项目管理',
    },
    {
      key: '/documents',
      icon: <FileTextOutlined />,
      label: '文档管理',
    },
    {
      key: '/chat',
      icon: <MessageOutlined />,
      label: '问答对话',
    },
    {
      key: 'review',
      icon: <TableOutlined />,
      label: '智能评审',
      children: [
        {
          key: '/self-evaluation',
          label: '自评表评审',
        },
        {
          key: '/project-review',
          label: '项目评审',
        },
      ],
    },
    {
      key: '/similarity',
      icon: <DiffOutlined />,
      label: '相似度检验',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  // 用户菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'theme',
      icon: <BulbOutlined />,
      label: theme === 'light' ? '切换到暗色主题' : '切换到亮色主题',
      onClick: () => setTheme(theme === 'light' ? 'dark' : 'light'),
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <AntLayout className="app-layout">
      <Header className="app-header">
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Button
            type="text"
            icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            style={{ marginRight: 16 }}
          />
          <div className="app-logo">
            <FileTextOutlined className="app-logo-icon" />
            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
              技术方案智能评审平台
            </Title>
          </div>
        </div>
        
        <Space>
          <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
            <Button type="text" icon={<UserOutlined />}>
              用户
            </Button>
          </Dropdown>
        </Space>
      </Header>
      
      <AntLayout>
        <Sider
          className="app-sider"
          collapsed={sidebarCollapsed}
          collapsible
          trigger={null}
          width={240}
          collapsedWidth={80}
        >
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            defaultOpenKeys={['review']}
            items={menuItems}
            onClick={handleMenuClick}
            style={{
              height: '100%',
              borderRight: 0,
              paddingTop: 16,
            }}
          />
        </Sider>
        
        <Content className="app-content">
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;