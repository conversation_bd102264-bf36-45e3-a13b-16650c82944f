import React from 'react';
import {
  Card,
  Typography,
  Space,
  Empty,
} from 'antd';
import {
  FileTextOutlined,
  BulbOutlined,
  TeamOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import type { DocumentSummary as DocumentSummaryType } from '../types';

const { Title, Paragraph } = Typography;

interface DocumentSummaryProps {
  summary: DocumentSummaryType;
  loading?: boolean;
}

const DocumentSummary: React.FC<DocumentSummaryProps> = ({ summary, loading = false }) => {
  // 检查摘要是否为空
  const isEmpty = !summary || (
    !summary.project_background?.trim() &&
    !summary.product_requirements?.trim() &&
    !summary.core_functions?.trim() &&
    !summary.target_users?.trim()
  );

  if (isEmpty && !loading) {
    return (
      <Card title="文档摘要" loading={loading}>
        <Empty
          description="暂无摘要信息"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  return (
    <Card title="文档摘要" loading={loading}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 项目背景 */}
        {summary?.project_background && (
          <div>
            <Title level={5}>
              <FileTextOutlined style={{ color: '#1890ff', marginRight: 8 }} />
              项目背景
            </Title>
            <Paragraph
              style={{
                background: '#f6f8fa',
                padding: 16,
                borderRadius: 6,
                borderLeft: '4px solid #1890ff',
                margin: 0,
              }}
            >
              {summary.project_background}
            </Paragraph>
          </div>
        )}

        {/* 产品需求 */}
        {summary?.product_requirements && (
          <div>
            <Title level={5}>
              <BulbOutlined style={{ color: '#52c41a', marginRight: 8 }} />
              产品需求
            </Title>
            <Paragraph
              style={{
                background: '#f6ffed',
                padding: 16,
                borderRadius: 6,
                borderLeft: '4px solid #52c41a',
                margin: 0,
              }}
            >
              {summary.product_requirements}
            </Paragraph>
          </div>
        )}

        {/* 产品核心功能 */}
        {summary?.core_functions && (
          <div>
            <Title level={5}>
              <SettingOutlined style={{ color: '#faad14', marginRight: 8 }} />
              产品核心功能
            </Title>
            <Paragraph
              style={{
                background: '#fffbe6',
                padding: 16,
                borderRadius: 6,
                borderLeft: '4px solid #faad14',
                margin: 0,
              }}
            >
              {summary.core_functions}
            </Paragraph>
          </div>
        )}

        {/* 用户群体 */}
        {summary?.target_users && (
          <div>
            <Title level={5}>
              <TeamOutlined style={{ color: '#722ed1', marginRight: 8 }} />
              用户群体
            </Title>
            <Paragraph
              style={{
                background: '#f9f0ff',
                padding: 16,
                borderRadius: 6,
                borderLeft: '4px solid #722ed1',
                margin: 0,
              }}
            >
              {summary.target_users}
            </Paragraph>
          </div>
        )}
      </Space>
    </Card>
  );
};

export default DocumentSummary;