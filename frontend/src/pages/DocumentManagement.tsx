import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Upload,
  Space,
  Tag,
  Popconfirm,
  Modal,
  Typography,
  Tooltip,
  Row,
  Col,
  Statistic,
  App,
  Input,
  Select,
  Divider,
} from 'antd';
import {
  UploadOutlined,
  DeleteOutlined,
  EyeOutlined,
  ThunderboltOutlined,
  FileTextOutlined,
  CloudUploadOutlined,
  ReloadOutlined,
  RedoOutlined,
  ExclamationCircleOutlined,
  SearchOutlined,
  BlockOutlined,
  DownOutlined,
  UpOutlined,
  FolderOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { UploadFile, UploadProps } from 'antd/es/upload';
import { documentApi } from '../utils/api';
import { useDocumentStore } from '../utils/store';
import {
  formatFileSize,
  formatTime,
  getDocumentStatusText,
  getDocumentStatusColor,
  validateFileType,
  validateFileSize,
} from '../utils/helpers';
import type { Document } from '../types';
import DuplicateDetectionResult from '../components/DuplicateDetectionResult';
import DocumentSummaryComponent from '../components/DocumentSummary';

const { Title, Paragraph } = Typography;
const { Dragger } = Upload;
const { Option } = Select;

interface Project {
  id: string;
  name: string;
  description?: string;
  document_count: number;
}

const DocumentManagement: React.FC = () => {
  const { message } = App.useApp();
  const {
    documents,
    loading,
    currentPage,
    pageSize,
    total,
    setDocuments,

    updateDocument,
    removeDocument,
    setLoading,
    setPagination,
  } = useDocumentStore();

  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [duplicateCheckLoading, setDuplicateCheckLoading] = useState<string | null>(null);
  const [summaryLoading, setSummaryLoading] = useState<string | null>(null);
  const [chunksModalVisible, setChunksModalVisible] = useState(false);
  const [documentChunks, setDocumentChunks] = useState<any[]>([]);
  const [chunksLoading, setChunksLoading] = useState(false);
  const [expandedChunks, setExpandedChunks] = useState<Set<string>>(new Set());
  const [searchKeyword, setSearchKeyword] = useState('');
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const [projectsLoading, setProjectsLoading] = useState(false);

  // 加载项目列表
  const loadProjects = async () => {
    try {
      setProjectsLoading(true);
      const response = await fetch('/api/projects');
      if (!response.ok) {
        throw new Error('获取项目列表失败');
      }
      const data = await response.json();
      setProjects(data.projects || []);
      
      // 如果没有选中项目且有项目列表，默认选择第一个项目
      if (!selectedProjectId && data.projects && data.projects.length > 0) {
        setSelectedProjectId(data.projects[0].id);
      }
    } catch (error) {
      console.error('加载项目列表失败:', error);
      message.error('加载项目列表失败');
    } finally {
      setProjectsLoading(false);
    }
  };

  // 加载文档列表
  const loadDocuments = async (page = 1, size = 10, projectId?: string) => {
    try {
      setLoading(true);
      const targetProjectId = projectId !== undefined ? projectId : selectedProjectId;
      
      // 构建查询参数
      const params = new URLSearchParams({
        page: page.toString(),
        page_size: size.toString(),
      });
      
      if (targetProjectId) {
        params.append('project_id', targetProjectId);
      }
      
      const response = await fetch(`/api/documents?${params}`);
      if (!response.ok) {
        throw new Error('获取文档列表失败');
      }
      
      const data = await response.json();
      setDocuments(data.documents || []);
      setPagination(page, size, data.total || 0);
    } catch (error) {
      console.error('加载文档列表失败:', error);
      message.error('加载文档列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 上传文档
  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.warning('请选择要上传的文件');
      return;
    }

    if (!selectedProjectId) {
      message.warning('请先选择项目');
      return;
    }

    setUploading(true);
    try {
      for (const file of fileList) {
        if (file.originFileObj) {
          // 构建FormData和URL查询参数
          const formData = new FormData();
          formData.append('file', file.originFileObj);
          
          // 将project_id作为查询参数添加到URL中
          const uploadUrl = `/api/documents/upload?project_id=${encodeURIComponent(selectedProjectId)}`;
          
          const response = await fetch(uploadUrl, {
            method: 'POST',
            body: formData,
          });
          
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '上传失败');
          }
          
          await response.json();
          message.success(`${file.name} 上传成功`);
        }
      }
      setFileList([]);
      setUploadModalVisible(false);
      loadDocuments(currentPage, pageSize);
      loadProjects(); // 刷新项目列表以更新文档数量
      loadProjects(); // 刷新项目列表以更新文档数量
      loadProjects(); // 刷新项目列表以更新文档数量
    } catch (error: any) {
      console.error('上传失败:', error);
      message.error(error.message || '上传失败');
    } finally {
      setUploading(false);
    }
  };

  // 向量化文档
  const handleVectorize = async (document: Document) => {
    try {
      updateDocument(document.id, { status: 'processing' });
      await documentApi.vectorize(document.id);
      message.success('文档向量化成功');
      loadDocuments(currentPage, pageSize);
    } catch (error) {
      console.error('向量化失败:', error);
      updateDocument(document.id, { status: 'failed' });
    }
  };

  // 重新向量化文档
  const handleRetryVectorize = async (document: Document) => {
    try {
      updateDocument(document.id, { status: 'processing' });
      await documentApi.vectorize(document.id);
      message.success('文档重新向量化成功');
      loadDocuments(currentPage, pageSize);
    } catch (error) {
      console.error('重新向量化失败:', error);
      updateDocument(document.id, { status: 'failed' });
      message.error('重新向量化失败，请稍后重试');
    }
  };

  // 删除文档
  const handleDelete = async (document: Document) => {
    try {
      await documentApi.delete(document.id);
      removeDocument(document.id);
      message.success('文档删除成功');
      loadProjects(); // 刷新项目列表以更新文档数量
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  // 查看文档详情
  const handleViewDetail = async (document: Document) => {
    try {
      const detail = await documentApi.getById(document.id);
      setSelectedDocument(detail);
      setDetailModalVisible(true);
    } catch (error) {
      console.error('获取文档详情失败:', error);
    }
  };

  // 检查重复性
  const handleCheckDuplicate = async (document: Document) => {
    try {
      setDuplicateCheckLoading(document.id);
      const result = await documentApi.checkDuplicate(document.id);
      
      // 更新本地文档数据
      updateDocument(document.id, {
        duplicate_detection: {
          is_duplicate: result.is_duplicate,
          similarity_score: result.similarity_score,
          duplicate_document_id: result.duplicate_document_id,
          duplicate_document_name: result.duplicate_document_name,
          similar_content: result.similar_content,
          detection_time: new Date().toISOString(),
        }
      });
      
      if (result.is_duplicate) {
        message.warning(`检测到重复内容，相似度: ${Math.round(result.similarity_score * 100)}%`);
      } else {
        message.success(`未检测到重复内容，相似度: ${Math.round(result.similarity_score * 100)}%`);
      }
    } catch (error) {
      console.error('重复检测失败:', error);
      message.error('重复检测失败，请稍后重试');
    } finally {
      setDuplicateCheckLoading(null);
    }
  };

  // 获取文档摘要
  const handleGetSummary = async (document: Document) => {
    try {
      setSummaryLoading(document.id);
      const result = await documentApi.getSummary(document.id);
      
      // 更新本地文档数据
      updateDocument(document.id, {
        summary: result.summary
      });
      
      message.success('文档摘要获取成功');
    } catch (error) {
      console.error('获取文档摘要失败:', error);
      message.error('获取文档摘要失败，请稍后重试');
    } finally {
      setSummaryLoading(null);
    }
  };

  // 查看文档分块
  const handleViewChunks = async (document: Document) => {
    try {
      setChunksLoading(true);
      const response = await fetch(`/api/documents/${document.id}/chunks`);
      const result = await response.json();
      
      if (result.success) {
        setDocumentChunks(result.chunks);
        setSelectedDocument(document);
        setChunksModalVisible(true);
        setExpandedChunks(new Set());
        setSearchKeyword('');
      } else {
        message.error('获取文档分块失败');
      }
    } catch (error) {
      console.error('获取文档分块失败:', error);
      message.error('获取文档分块失败，请稍后重试');
    } finally {
      setChunksLoading(false);
    }
  };

  // 切换分块展开状态
  const toggleChunkExpansion = (chunkId: string) => {
    const newExpanded = new Set(expandedChunks);
    if (newExpanded.has(chunkId)) {
      newExpanded.delete(chunkId);
    } else {
      newExpanded.add(chunkId);
    }
    setExpandedChunks(newExpanded);
  };

  // 过滤分块
  const filteredChunks = documentChunks.filter(chunk => 
    !searchKeyword || chunk.content.toLowerCase().includes(searchKeyword.toLowerCase())
  );

  // 上传配置
  const uploadProps: UploadProps = {
    multiple: true,
    fileList,
    beforeUpload: (file) => {
      // 验证文件类型
      if (!validateFileType(file, ['.docx'])) {
        message.error('只支持 .docx 格式的文件');
        return false;
      }
      
      // 验证文件大小 (50MB)
      if (!validateFileSize(file, 50 * 1024 * 1024)) {
        message.error('文件大小不能超过 50MB');
        return false;
      }
      
      return false; // 阻止自动上传
    },
    onChange: ({ fileList: newFileList }) => {
      setFileList(newFileList);
    },
    onRemove: (file) => {
      setFileList(fileList.filter(item => item.uid !== file.uid));
    },
  };

  // 表格列配置
  const columns: ColumnsType<Document> = [
    {
      title: '文档名称',
      dataIndex: 'filename',
      key: 'filename',
      ellipsis: true,
      render: (text: string, record: Document) => {
        // 优先使用original_name，如果没有则从filename中提取
        const displayName = record.original_name || 
          (text ? text.replace(/^[a-f0-9-]+_/, '') : text);
        
        return (
          <Tooltip title={text}>
            <FileTextOutlined style={{ marginRight: 8 }} />
            {displayName}
          </Tooltip>
        );
      },
    },
    {
      title: '文件大小',
      dataIndex: 'file_size',
      key: 'file_size',
      width: 120,
      render: (size: number) => formatFileSize(size),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string, record: Document) => (
        <Space direction="vertical" size={0}>
          <Tag color={getDocumentStatusColor(status || 'unknown')}>
            {getDocumentStatusText(status || 'unknown')}
          </Tag>
          {record?.duplicate_detection && (
            <Tag 
              color={record.duplicate_detection.is_duplicate ? 'red' : 'green'}
            >
              {record.duplicate_detection.is_duplicate ? '重复' : '唯一'}
            </Tag>
          )}
        </Space>
      ),
    },
    {
      title: '上传时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (time: string) => formatTime(time),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => {
        // 添加空值检查，确保record存在且有id属性
        if (!record || !record.id) {
          return <span>-</span>;
        }
        
        return (
          <Space wrap>
            <Tooltip key={`view-${record.id}`} title="查看详情">
              <Button
                type="text"
                icon={<EyeOutlined />}
                onClick={() => handleViewDetail(record)}
              />
            </Tooltip>
            
            {record.status === 'uploaded' && (
              <Tooltip key={`vectorize-${record.id}`} title="向量化">
                <Button
                  type="text"
                  icon={<ThunderboltOutlined />}
                  onClick={() => handleVectorize(record)}
                />
              </Tooltip>
            )}
            
            {record.status === 'completed' && (
              <>
                <Tooltip key={`chunks-${record.id}`} title="查看分块">
                  <Button
                    type="text"
                    icon={<BlockOutlined />}
                    onClick={() => handleViewChunks(record)}
                    loading={chunksLoading}
                    style={{ color: '#52c41a' }}
                  />
                </Tooltip>
                <Tooltip key={`duplicate-${record.id}`} title="重复检测">
                  <Button
                    type="text"
                    icon={record?.duplicate_detection?.is_duplicate ? <ExclamationCircleOutlined /> : <SearchOutlined />}
                    onClick={() => handleCheckDuplicate(record)}
                    loading={duplicateCheckLoading === record.id}
                    style={{ 
                      color: record?.duplicate_detection?.is_duplicate ? '#ff4d4f' : '#1890ff'
                    }}
                  />
                </Tooltip>
              </>
            )}
            
            {record.status === 'failed' && (
              <Tooltip key={`retry-${record.id}`} title="重新向量化">
                <Button
                  type="text"
                  icon={<RedoOutlined />}
                  onClick={() => handleRetryVectorize(record)}
                  style={{ color: '#1890ff' }}
                />
              </Tooltip>
            )}
            
            <Popconfirm
              key={`delete-${record.id}`}
              title="确定要删除这个文档吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  // 处理项目切换
  const handleProjectChange = (projectId: string) => {
    setSelectedProjectId(projectId);
    loadDocuments(1, pageSize, projectId); // 切换项目时重置到第一页
  };

  useEffect(() => {
    loadProjects();
  }, []);

  useEffect(() => {
    if (selectedProjectId) {
      loadDocuments(currentPage, pageSize, selectedProjectId);
    }
  }, [selectedProjectId]);

  // 统计数据 - 添加更严格的空值检查
  const stats = {
    total: documents.length,
    completed: documents.filter(doc => doc && doc.id && doc.status === 'completed').length,
    processing: documents.filter(doc => doc && doc.id && doc.status === 'processing').length,
    failed: documents.filter(doc => doc && doc.id && doc.status === 'failed').length,
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={2} className="page-title">
          文档管理
        </Title>
        <Paragraph className="page-description">
          上传和管理您的文档，支持 DOCX 格式文件的向量化处理
        </Paragraph>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总文档数"
              value={stats.total}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.completed}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="处理中"
              value={stats.processing}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="失败"
              value={stats.failed}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 项目选择器和操作按钮 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Space>
              <FolderOutlined style={{ color: '#1890ff' }} />
              <span style={{ fontWeight: 500 }}>当前项目：</span>
              <Select
                value={selectedProjectId}
                onChange={handleProjectChange}
                loading={projectsLoading}
                style={{ minWidth: 200 }}
                placeholder="请选择项目"
              >
                {projects.map(project => (
                  <Option key={project.id} value={project.id}>
                    <Space>
                      <FolderOutlined />
                      {project.name}
                      <Tag color="blue">{project.document_count}</Tag>
                    </Space>
                  </Option>
                ))}
              </Select>
            </Space>
          </Col>
          <Col span={16} style={{ textAlign: 'right' }}>
            <Space>
              <Button
                type="primary"
                icon={<UploadOutlined />}
                onClick={() => setUploadModalVisible(true)}
                disabled={!selectedProjectId}
              >
                上传文档
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => loadDocuments(currentPage, pageSize)}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
        
        {selectedProjectId && (
          <div style={{ marginTop: 12 }}>
            <Divider style={{ margin: '12px 0' }} />
            <Space>
              <span style={{ color: '#666' }}>项目描述：</span>
              <span>{projects.find(p => p.id === selectedProjectId)?.description || '暂无描述'}</span>
            </Space>
          </div>
        )}
      </Card>

      {/* 文档列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={documents}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              loadDocuments(page, size || pageSize);
            },
          }}
        />
      </Card>

      {/* 上传模态框 */}
      <Modal
        title="上传文档"
        open={uploadModalVisible}
        onOk={handleUpload}
        onCancel={() => {
          setUploadModalVisible(false);
          setFileList([]);
        }}
        confirmLoading={uploading}
        okText="开始上传"
        cancelText="取消"
        width={600}
      >
        <Dragger {...uploadProps} style={{ marginBottom: 16 }}>
          <p className="ant-upload-drag-icon">
            <CloudUploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持 .docx 格式，单个文件不超过 50MB
          </p>
        </Dragger>
        
        {fileList.length > 0 && (
          <div>
            <Title level={5}>待上传文件：</Title>
            {fileList.map((file, index) => (
              <div key={file.uid || `file-${index}-${file.name || 'unknown'}`} style={{ marginBottom: 8 }}>
                <FileTextOutlined style={{ marginRight: 8 }} />
                {file.name} ({formatFileSize(file.size || 0)})
              </div>
            ))}
          </div>
        )}
      </Modal>

      {/* 文档详情模态框 */}
      <Modal
        title="文档详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={1200}
      >
        {selectedDocument && (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <p><strong>文档名称：</strong>{selectedDocument.original_name || selectedDocument.filename?.replace(/^[a-f0-9-]+_/, '') || selectedDocument.filename}</p>
                <p><strong>文件大小：</strong>{formatFileSize(selectedDocument.file_size)}</p>
                <p><strong>状态：</strong>
                  <Tag color={getDocumentStatusColor(selectedDocument.status)}>
                    {getDocumentStatusText(selectedDocument.status)}
                  </Tag>
                </p>
              </Col>
              <Col span={12}>
                <p><strong>上传时间：</strong>{formatTime(selectedDocument.created_at)}</p>
                <p><strong>更新时间：</strong>{formatTime(selectedDocument.updated_at)}</p>
                {selectedDocument.chunk_count > 0 && (
                  <p><strong>文本块数量：</strong>{selectedDocument.chunk_count}</p>
                )}
              </Col>
            </Row>
            
            {/* 操作按钮 */}
            {selectedDocument.status === 'completed' && (
              <div style={{ marginTop: 16, marginBottom: 16 }}>
                <Space>
                  {!selectedDocument?.summary && (
                    <Button
                      type="primary"
                      icon={<FileTextOutlined />}
                      onClick={() => handleGetSummary(selectedDocument)}
                      loading={summaryLoading === selectedDocument.id}
                    >
                      生成摘要
                    </Button>
                  )}
                  {!selectedDocument?.duplicate_detection && (
                    <Button
                      icon={<SearchOutlined />}
                      onClick={() => handleCheckDuplicate(selectedDocument)}
                      loading={duplicateCheckLoading === selectedDocument.id}
                    >
                      重复检测
                    </Button>
                  )}
                </Space>
              </div>
            )}
            
            {/* 文档摘要 */}
            {selectedDocument?.summary && (
              <DocumentSummaryComponent 
                summary={selectedDocument.summary}
                loading={summaryLoading === selectedDocument.id}
              />
            )}
            
            {/* 重复检测结果 */}
            {selectedDocument?.duplicate_detection && (
              <DuplicateDetectionResult
                result={selectedDocument.duplicate_detection}
                documentName={selectedDocument.original_name}
              />
            )}
            
            {selectedDocument.content && (
              <Card title="文档内容预览" style={{ marginTop: 16 }}>
                <div style={{ 
                  maxHeight: 300, 
                  overflow: 'auto', 
                  padding: 12, 
                  background: '#f5f5f5', 
                  borderRadius: 4 
                }}>
                  {selectedDocument.content.substring(0, 1000)}
                  {selectedDocument.content.length > 1000 && '...'}
                </div>
              </Card>
            )}
          </div>
        )}
      </Modal>

      {/* 文档分块弹窗 */}
      <Modal
        title={`文档分块详情 - ${selectedDocument?.original_name || selectedDocument?.filename?.replace(/^[a-f0-9-]+_/, '') || selectedDocument?.filename || ''}`}
        open={chunksModalVisible}
        onCancel={() => {
          setChunksModalVisible(false);
          setDocumentChunks([]);
          setSelectedDocument(null);
          setExpandedChunks(new Set());
          setSearchKeyword('');
        }}
        footer={[
          <Button key="close" onClick={() => {
            setChunksModalVisible(false);
            setDocumentChunks([]);
            setSelectedDocument(null);
          }}>
            关闭
          </Button>,
        ]}
        width={1000}
        style={{ top: 20 }}
      >
        {selectedDocument && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Tag color="blue">总分块数: {documentChunks.length}</Tag>
                <Tag color="green">文档: {selectedDocument.original_name || selectedDocument.filename?.replace(/^[a-f0-9-]+_/, '') || selectedDocument.filename}</Tag>
              </Space>
            </div>
            
            {/* 搜索框 */}
            <div style={{ marginBottom: 16 }}>
              <Input
                placeholder="搜索分块内容..."
                prefix={<SearchOutlined />}
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                allowClear
              />
            </div>
            
            <div style={{ maxHeight: '60vh', overflow: 'auto' }}>
              {filteredChunks.map((chunk, index) => {
                const chunkId = chunk.id || `chunk-${index}`;
                const isExpanded = expandedChunks.has(chunkId);
                const contentPreview = chunk.content?.length > 200 ? 
                  chunk.content.substring(0, 200) + '...' : chunk.content;
                
                return (
                  <Card
                    key={chunkId}
                    size="small"
                    title={`分块 ${chunk.chunk_index + 1}`}
                    extra={
                      <Space>
                        <Tag color="orange">长度: {chunk.content_length}</Tag>
                        {chunk.created_time && (
                          <Tag color="purple">创建: {new Date(chunk.created_time).toLocaleString()}</Tag>
                        )}
                        {chunk.content?.length > 200 && (
                          <Button
                            type="text"
                            size="small"
                            icon={isExpanded ? <UpOutlined /> : <DownOutlined />}
                            onClick={() => toggleChunkExpansion(chunkId)}
                          >
                            {isExpanded ? '收起' : '展开'}
                          </Button>
                        )}
                      </Space>
                    }
                    style={{ marginBottom: 12 }}
                  >
                    <div 
                      style={{ 
                        maxHeight: isExpanded ? 'none' : 200, 
                        overflow: isExpanded ? 'visible' : 'auto', 
                        padding: 8, 
                        background: '#f9f9f9', 
                        borderRadius: 4,
                        whiteSpace: 'pre-wrap',
                        fontSize: '14px',
                        lineHeight: '1.5'
                      }}
                    >
                      {isExpanded ? (chunk.content || '无内容') : (contentPreview || '无内容')}
                    </div>
                    
                    {chunk.metadata && Object.keys(chunk.metadata).length > 0 && (
                      <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                        <strong>元数据:</strong>
                        <div style={{ marginTop: 4 }}>
                          {Object.entries(chunk.metadata).map(([key, value]) => (
                             <Tag key={key}>
                               {key}: {String(value)}
                             </Tag>
                           ))}
                        </div>
                      </div>
                    )}
                  </Card>
                );
              })}
              
              {filteredChunks.length === 0 && (
                <div style={{ textAlign: 'center', padding: 40, color: '#999' }}>
                  {searchKeyword ? '未找到匹配的分块' : '暂无分块数据'}
                </div>
              )}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default DocumentManagement;