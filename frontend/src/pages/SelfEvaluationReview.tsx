import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Typography,
  Space,
  Alert,
  Table as AntTable,
  Tag,
  Row,
  Col,
  Spin,
  App,
  Select,
} from 'antd';
import {
  DownloadOutlined,
  FileTextOutlined,
  TableOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  UploadOutlined,
  FolderOutlined,
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

interface TableData {
  success: boolean;
  preview_data?: string[][];
  total_rows?: number;
  total_columns?: number;
  total_tables?: number;
  error?: string;
}

interface ProcessResult {
  success: boolean;
  message?: string;
  output_path?: string;
  output_filename?: string;
  tables_count?: number;
  tables?: string[][];
  error?: string;
}

interface Project {
  id: string;
  name: string;
  description?: string;
  document_count: number;
}

const SelfEvaluationReview: React.FC = () => {
  const { message } = App.useApp();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [tableData, setTableData] = useState<TableData | null>(null);
  const [processResult, setProcessResult] = useState<ProcessResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<string | undefined>(undefined);
  const [loadingProjects, setLoadingProjects] = useState(false);

  // 获取项目列表
  const fetchProjects = async () => {
    setLoadingProjects(true);
    try {
      const response = await fetch('/api/projects');
      if (!response.ok) {
        throw new Error('获取项目列表失败');
      }
      const data = await response.json();
      setProjects(data.projects || []);
    } catch (error) {
      console.error('获取项目列表失败:', error);
      message.error('获取项目列表失败');
    } finally {
      setLoadingProjects(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  const handleFileSelect = (file: File) => {
    if (!file.name.endsWith('.docx') && !file.name.endsWith('.doc')) {
      message.error('请选择Word文档文件（.docx或.doc格式）');
      return false;
    }
    setSelectedFile(file);
    setTableData(null);
    setProcessResult(null);
    message.success('文件选择成功');
    return false; // 阻止默认上传行为
  };

  const handleUploadClick = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.doc,.docx';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        handleFileSelect(file);
      }
    };
    input.click();
  };

  const previewTable = async () => {
    if (!selectedFile) {
      message.error('请先选择文件');
      return;
    }

    setIsLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await fetch(
        `/api/table/upload-and-preview?table_index=0&max_rows=10`,
        {
          method: 'POST',
          body: formData,
        }
      );

      if (!response.ok) {
        throw new Error('预览失败');
      }

      const result: TableData = await response.json();
      setTableData(result);

      if (result.success) {
        message.success('表格预览成功');
      } else {
        message.error(result.error || '预览失败');
      }
    } catch (error) {
      console.error('预览表格失败:', error);
      message.error('预览表格失败');
    } finally {
      setIsLoading(false);
    }
  };

  const processTable = async () => {
    if (!selectedFile) {
      message.error('请先选择文件');
      return;
    }

    if (!selectedProject) {
      message.error('请先选择项目');
      return;
    }

    setIsProcessing(true);
    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('project_id', selectedProject);

      const response = await fetch('/api/table/process', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('处理失败');
      }

      const result: ProcessResult = await response.json();
      setProcessResult(result);

      if (result.success) {
        message.success('表格处理成功！可以下载Word文件了');
      } else {
        message.error(result.error || '处理失败');
      }
    } catch (error) {
      console.error('处理表格失败:', error);
      message.error('处理表格失败');
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadDocx = async () => {
    if (!processResult?.output_filename) {
      message.error('没有可下载的文件');
      return;
    }

    try {
      const response = await fetch(`/api/table/download/${processResult.output_filename}`);
      
      if (!response.ok) {
        throw new Error('下载失败');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = processResult.output_filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      message.success('Word文件下载成功');
    } catch (error) {
      console.error('下载文件失败:', error);
      message.error('下载文件失败');
    }
  };

  return (
    <div className="page-container">
      <Row gutter={16}>
        <Col span={24}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <TableOutlined style={{ fontSize: 20, color: '#1890ff' }} />
                <Title level={4} style={{ margin: 0 }}>
                  自评表评审
                </Title>
              </div>
            }
            className="custom-card"
          >
            {/* 项目选择区域 */}
            <div style={{ marginBottom: 24 }}>
              <div style={{ marginBottom: 16 }}>
                <Text strong style={{ fontSize: '16px' }}>
                  <FolderOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                  选择项目
                </Text>
              </div>
              <Select
                placeholder="请选择要评审的项目"
                style={{ width: '100%', marginBottom: 16 }}
                size="large"
                loading={loadingProjects}
                value={selectedProject}
                onChange={setSelectedProject}
                showSearch
                filterOption={(input, option) => {
                  const label = typeof option?.label === 'string' ? option.label : '';
                  return label.toLowerCase().includes(input.toLowerCase());
                }}
                options={projects.map(project => ({
                  value: project.id,
                  label: (
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>{project.name}</span>
                      <Tag color="blue">{project.document_count} 个文档</Tag>
                    </div>
                  ),
                }))}
              />
              {selectedProject && (
                <Alert
                  message="项目已选择"
                  description={`将基于 "${projects.find(p => p.id === selectedProject)?.name}" 项目中的文档进行智能评审`}
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              )}
            </div>

            {/* 文件上传区域 */}
            <div style={{ marginBottom: 24, textAlign: 'center' }}>
              <Button
                type="primary"
                icon={<UploadOutlined />}
                onClick={handleUploadClick}
                size="large"
                style={{ marginBottom: 16 }}
                disabled={!selectedProject}
              >
                选择Word文档
              </Button>
              <div style={{ color: '#666', fontSize: '14px', marginBottom: 16 }}>
                支持 .doc 和 .docx 格式文件
              </div>
              {selectedFile && (
                <div style={{ textAlign: 'center' }}>
                  <Tag icon={<FileTextOutlined />} color="blue">
                    已选择: {selectedFile.name}
                  </Tag>
                </div>
              )}
            </div>

            {/* 处理说明 */}
            {selectedFile && (
              <Alert
                message="处理说明"
                description={
                  <Paragraph style={{ margin: 0 }}>
                    系统将基于选定项目的文档，自动为Word文档中的表格添加 <Text strong>dp_out</Text> 和 <Text strong>dp_reason</Text> 两列，
                    提供专业的评审意见和判断依据。处理完成后可下载修改后的Word文档。
                  </Paragraph>
                }
                type="info"
                showIcon
                style={{ marginBottom: 24 }}
              />
            )}

            {/* 操作按钮 */}
            {selectedFile && (
              <Space size="middle" style={{ marginBottom: 24 }}>
                <Button
                  type="default"
                  icon={isLoading ? <Spin size="small" /> : <FileTextOutlined />}
                  onClick={previewTable}
                  loading={isLoading}
                  size="large"
                  className="action-button"
                >
                  预览表格
                </Button>
                
                <Button
                  type="primary"
                  icon={isProcessing ? <Spin size="small" /> : <TableOutlined />}
                  onClick={processTable}
                  loading={isProcessing}
                  size="large"
                  className="action-button"
                  disabled={!selectedProject}
                >
                  添加列并处理
                </Button>
              </Space>
            )}

            {/* 预览结果 */}
            {tableData && (
              <Card
                title={
                  <Space>
                    {tableData.success ? (
                      <CheckCircleOutlined style={{ color: '#52c41a' }} />
                    ) : (
                      <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                    )}
                    <span>表格预览</span>
                  </Space>
                }
                style={{ marginBottom: 24 }}
                className="custom-card"
              >
                {tableData.success ? (
                  <div>
                    <div style={{ marginBottom: 16 }}>
                      <Text type="secondary">
                        表格信息: {tableData.total_rows} 行 × {tableData.total_columns} 列
                        {tableData.total_tables && tableData.total_tables > 1 && (
                          <span> (文档共有 {tableData.total_tables} 个表格)</span>
                        )}
                      </Text>
                    </div>
                    
                    <AntTable
                      dataSource={tableData.preview_data?.map((row, index) => ({
                        key: index,
                        ...row.reduce((acc, cell, cellIndex) => {
                          acc[`col_${cellIndex}`] = cell;
                          return acc;
                        }, {} as Record<string, string>)
                      }))}
                      columns={tableData.preview_data?.[0]?.map((_, index) => ({
                        title: `列 ${index + 1}`,
                        dataIndex: `col_${index}`,
                        key: `col_${index}`,
                        ellipsis: true,
                      })) || []}
                      pagination={false}
                      scroll={{ x: true }}
                      size="small"
                    />
                  </div>
                ) : (
                  <Alert
                    message="预览失败"
                    description={tableData.error}
                    type="error"
                    showIcon
                  />
                )}
              </Card>
            )}

            {/* 处理结果 */}
            {processResult && (
              <Card
                title={
                  <Space>
                    {processResult.success ? (
                      <CheckCircleOutlined style={{ color: '#52c41a' }} />
                    ) : (
                      <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                    )}
                    <span>处理结果</span>
                  </Space>
                }
                style={{ marginBottom: 24 }}
                className="custom-card"
              >
                {processResult.success ? (
                  <div>
                    <Alert
                      message={processResult.message || '表格处理成功！'}
                      description={
                        <div>
                          <Text>已为表格添加 dp_out 和 dp_reason 两列</Text>
                          {processResult.tables_count && (
                            <Text>，共处理 {processResult.tables_count} 个表格</Text>
                          )}
                        </div>
                      }
                      type="success"
                      showIcon
                      style={{ marginBottom: 16 }}
                    />
                    
                    <Button
                      type="primary"
                      icon={<DownloadOutlined />}
                      onClick={downloadDocx}
                      size="large"
                      style={{ backgroundColor: '#722ed1', borderColor: '#722ed1' }}
                    >
                      下载Word文档
                    </Button>
                  </div>
                ) : (
                  <Alert
                    message="处理失败"
                    description={processResult.error}
                    type="error"
                    showIcon
                  />
                )}
              </Card>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SelfEvaluationReview;