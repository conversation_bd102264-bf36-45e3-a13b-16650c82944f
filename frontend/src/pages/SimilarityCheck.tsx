import React, { useState, useEffect } from 'react';
import {
  Card,
  Upload,
  Button,
  Typography,
  Tag,
  Space,
  message,
  Spin,
  Checkbox,
  Collapse,
  Alert,
  Select,
  Row,
  Col,
  Divider,
} from 'antd';
import {
  FileTextOutlined,
  DiffOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  InboxOutlined,
  DownOutlined,
  UpOutlined,
  FolderOutlined,
} from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import type { UploadFile } from 'antd';
import type { Document } from '../types';
import '../styles/SimilarityCheck.css';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;
// const { Dragger } = Upload; // 暂时注释掉未使用的变量

// 项目接口定义
interface Project {
  id: string;
  name: string;
  description?: string;
  document_count: number;
  created_time: string;
}

interface SimilarityResult {
  similarity_score: number;
  is_duplicate: boolean;  // 50%以上禁止导入
  needs_review?: boolean; // 30%以上需要人工审核
  similar_content: Array<{
    type: string;
    current_content: string;
    similar_content: string;
    similarity_score: number;
  }>;
  document1_name: string;
  document2_name: string;
  analysis_time: string;
  ai_analysis?: {
    is_duplicate: boolean;
    analysis: string;
    analysis_time: string;
  };
}

const SimilarityCheck: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [, setDocuments] = useState<Document[]>([]); // documents变量暂时未使用，只使用setDocuments
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<SimilarityResult[]>([]);
  const [overallSimilarity, setOverallSimilarity] = useState<number>(0);
  const [reviewConfirmed, setReviewConfirmed] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploadKey, setUploadKey] = useState<number>(0);
  const [expandedContent, setExpandedContent] = useState<{[key: string]: boolean}>({});
  const [aiAnalysisLoading, setAiAnalysisLoading] = useState(false);
  const [hasAiAnalysis, setHasAiAnalysis] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');
  const [projectsLoading, setProjectsLoading] = useState(false);





  // 文档上传前验证
  const beforeUpload = (uploadFile: File) => {
    const isDocx = uploadFile.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    const isPdf = uploadFile.type === 'application/pdf';
    const isDoc = uploadFile.type === 'application/msword';
    if (!isDocx && !isPdf && !isDoc) {
      message.error('只支持上传 PDF、Word 格式文件！');
      return false;
    }
    const isLt10M = uploadFile.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过 10MB！');
      return false;
    }
    return false; // 阻止自动上传
  };

  const handleFileChange = (info: any) => {
    const { fileList } = info;
    setFileList(fileList);
    
    if (fileList.length > 0) {
      const selectedFile = fileList[0].originFileObj;
      setFile(selectedFile);
      // 自动开始相似度检测
      handleSimilarityCheck(selectedFile);
    } else {
      // 完全重置所有状态
      resetAllStates();
    }
  };

  // 加载项目列表
  const loadProjects = async () => {
    try {
      setProjectsLoading(true);
      const response = await fetch('/api/projects');
      if (response.ok) {
        const data = await response.json();
        setProjects(data.projects || []);
        // 如果没有选中项目且有项目列表，默认选择第一个
        if (!selectedProjectId && data.projects && data.projects.length > 0) {
          setSelectedProjectId(data.projects[0].id);
        }
      }
    } catch (error) {
      console.error('加载项目列表失败:', error);
    } finally {
      setProjectsLoading(false);
    }
  };

  // 处理项目切换
  const handleProjectChange = (projectId: string) => {
    setSelectedProjectId(projectId);
    // 切换项目时重置所有状态
    resetAllStates();
  };

  // 重置所有状态的函数
  const resetAllStates = () => {
    setFile(null);
    setResults([]);
    setOverallSimilarity(0);
    setReviewConfirmed(false);
    setUploadSuccess(false);
    setFileList([]);
    setUploadKey(prev => prev + 1); // 强制重新渲染Upload组件
    setExpandedContent({});
    setAiAnalysisLoading(false);
    setHasAiAnalysis(false);
  };

  const toggleContentExpansion = (resultIndex: number, contentIndex: number, type: string) => {
    const key = `${resultIndex}-${contentIndex}-${type}`;
    setExpandedContent(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const truncateContent = (content: string, maxLength: number = 200) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  // 获取已向量化的文档列表
  const fetchDocuments = async () => {
    try {
      const response = await fetch('/api/documents?page=1&page_size=100');
      const data = await response.json();
      if (data.documents) {
        // 只显示已完成向量化的文档
        const completedDocs = data.documents.filter((doc: Document) => doc.status === 'completed');
        setDocuments(completedDocs);
      }
    } catch (error) {
      console.error('获取文档列表失败:', error);
      message.error('获取文档列表失败');
    }
  };

  useEffect(() => {
    loadProjects();
    fetchDocuments();
  }, []);

  useEffect(() => {
    // 当选择项目后，重新获取该项目的文档列表
    if (selectedProjectId) {
      fetchDocuments();
    }
  }, [selectedProjectId]);



  const handleSimilarityCheck = async (selectedFile?: File) => {
    const targetFile = selectedFile || file;
    if (!targetFile) {
      message.error('请先选择文档');
      return;
    }

    if (!selectedProjectId) {
      message.error('请先选择一个项目');
      return;
    }

    setLoading(true);
    setResults([]);
    setOverallSimilarity(0);
    setReviewConfirmed(false);
    setUploadSuccess(false);
    
    try {
      // 创建FormData用于文件上传
      const formData = new FormData();
      formData.append('file', targetFile);
      formData.append('project_id', selectedProjectId);
      
      // 调用后端相似度检测API
      const response = await fetch('/api/similarity/check', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success && result.data) {
        // 计算总体相似度（使用后端返回的总体相似度）
        const totalSimilarity = result.data.similarity_score * 100; // 转换为百分比
        setOverallSimilarity(totalSimilarity);
        
        // 如果相似度很低，创建基本结果但不包含AI分析
        if (totalSimilarity < 30) {
          // 创建一个基本结果，不包含AI分析
          const basicResult: SimilarityResult = {
            similarity_score: totalSimilarity,
            is_duplicate: false,
            needs_review: false,
            similar_content: [],
            document1_name: targetFile.name,
            document2_name: result.data.duplicate_document_name || '已存储文档',
            analysis_time: new Date().toISOString(),
            ai_analysis: undefined // 默认不包含AI分析
          };
          
          setResults([basicResult]);
          message.info(`相似度检测完成，相似度: ${totalSimilarity.toFixed(1)}%，未检测到显著相似内容`);
          return;
        }
        
        // 处理相似内容数据
        if (result.data.similar_content && Array.isArray(result.data.similar_content) && result.data.similar_content.length > 0) {
          // 转换后端数据格式为前端格式
          const similarityResults: SimilarityResult[] = result.data.similar_content.map((item: any) => ({
            similarity_score: totalSimilarity,
            is_duplicate: totalSimilarity >= 50, // 50%阈值，禁止导入
            needs_review: totalSimilarity >= 30, // 30%阈值，需要审核
            similar_content: [{
              type: item.type || '内容对比',
              current_content: item.current_content || '',
              similar_content: item.similar_content || '',
              similarity_score: totalSimilarity
            }],
            document1_name: targetFile.name,
            document2_name: result.data.duplicate_document_name || '已存储文档',
            analysis_time: new Date().toISOString(),
            ai_analysis: undefined // 默认不包含AI分析，需要用户手动触发
          }));
          
          setResults(similarityResults);
        } else {
          // 如果没有具体的相似内容，但有总体相似度，创建一个基本结果
          const basicResult: SimilarityResult = {
            similarity_score: totalSimilarity,
            is_duplicate: totalSimilarity >= 50,
            needs_review: totalSimilarity >= 30,
            similar_content: [],
            document1_name: targetFile.name,
            document2_name: result.data.duplicate_document_name || '已存储文档',
            analysis_time: new Date().toISOString(),
            ai_analysis: undefined // 默认不包含AI分析，需要用户手动触发
          };
          
          setResults([basicResult]);
        }
        
      } else {
        // 如果没有检测到相似内容，显示无相似内容的结果
        setResults([]);
        setOverallSimilarity(0);
        message.info('未检测到相似内容');
      }
      
    } catch (error) {
      console.error('相似度检测失败:', error);
      message.error('相似度检测失败，请重试');
      // 如果API调用失败，显示无相似内容
      setResults([]);
      setOverallSimilarity(0);
    } finally {
      setLoading(false);
    }
  };

  // 手动触发AI分析
  const handleGenerateAiAnalysis = async () => {
    if (!file || results.length === 0) {
      message.error('请先进行相似度检测');
      return;
    }

    setAiAnalysisLoading(true);
    try {
      // 创建FormData用于文件上传
      const formData = new FormData();
      formData.append('file', file);
      
      // 调用后端AI分析API
      const response = await fetch('/api/similarity/ai-analysis', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success && result.data && result.data.ai_analysis) {
        // 更新结果中的AI分析
        setResults(prevResults => 
          prevResults.map(prevResult => ({
            ...prevResult,
            ai_analysis: {
              is_duplicate: result.data.ai_analysis.is_duplicate_construction || false,
              analysis: result.data.ai_analysis.analysis_result || '',
              analysis_time: result.data.ai_analysis.analysis_time || new Date().toISOString()
            }
          }))
        );
        setHasAiAnalysis(true);
        message.success('AI智能分析完成');
      } else {
        throw new Error(result.message || 'AI分析失败');
      }
      
    } catch (error) {
      console.error('AI分析失败:', error);
      message.error('AI分析失败，请重试');
    } finally {
      setAiAnalysisLoading(false);
    }
  };

  const handleUploadToSystem = async () => {
    if (!file) {
      message.error('请先选择文档');
      return;
    }

    if (!selectedProjectId) {
      message.error('请先选择一个项目');
      return;
    }

    if (overallSimilarity >= 50) {
      message.error('相似度过高，无法上传此文档');
      return;
    }
    
    if (overallSimilarity >= 30 && !reviewConfirmed) {
      message.error('请先审核相似内容后再上传');
      return;
    }

    setUploading(true);
    try {
      // 创建FormData用于文件上传
      const formData = new FormData();
      formData.append('file', file);
      formData.append('project_id', selectedProjectId);
      
      // 调用后端文档上传API
      const response = await fetch('/api/documents/upload', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        message.success('文档上传成功！');
        setUploadSuccess(true);
        
        // 重置状态
        setTimeout(() => {
          resetAllStates();
          // 刷新文档列表
          fetchDocuments();
        }, 2000);
      } else {
        throw new Error(result.message || '上传失败');
      }
      
    } catch (error) {
      console.error('文档上传失败:', error);
      message.error('文档上传失败，请重试');
    } finally {
      setUploading(false);
    }
  };







  return (
    <div className="similarity-check-container">
      <div className="page-header">
        <Title level={2} className="page-title">
          <DiffOutlined /> 相似度检验
        </Title>
        <Paragraph className="page-description">
          上传DOCX文档，系统将与已向量化的文档库进行相似度对比，帮助您识别重复内容和相似结构
        </Paragraph>
      </div>

      {/* 项目选择器 */}
      <Card className="mb-6">
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Space>
              <FolderOutlined style={{ color: '#1890ff' }} />
              <span style={{ fontWeight: 500 }}>当前项目：</span>
              <Select
                value={selectedProjectId}
                onChange={handleProjectChange}
                loading={projectsLoading}
                style={{ minWidth: 200 }}
                placeholder="请选择项目"
              >
                {projects.map(project => (
                  <Option key={project.id} value={project.id}>
                    <Space>
                      <FolderOutlined />
                      {project.name}
                      <Tag color="blue">{project.document_count}</Tag>
                    </Space>
                  </Option>
                ))}
              </Select>
            </Space>
          </Col>
          <Col span={16}>
            {selectedProjectId && (
              <div>
                <Divider style={{ margin: '12px 0' }} />
                <Space>
                  <span style={{ color: '#666' }}>项目描述：</span>
                  <span>{projects.find(p => p.id === selectedProjectId)?.description || '暂无描述'}</span>
                </Space>
              </div>
            )}
          </Col>
        </Row>
      </Card>

      {/* 文档上传区域 */}
        <Card className="mb-6">
          <div className="text-center">
            <Upload.Dragger
              key={uploadKey}
              name="file"
              multiple={false}
              onChange={handleFileChange}
              beforeUpload={beforeUpload}
              showUploadList={false}
              fileList={fileList}
              className="mb-4"
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
              </p>
              <p className="ant-upload-text">点击或拖拽文档到此区域</p>
              <p className="ant-upload-hint">
                支持 PDF、Word 格式，文件大小不超过 10MB
                <br />
                <span className="text-blue-600">选择文档后将自动进行相似度检测</span>
              </p>
            </Upload.Dragger>
            
            {file && (
              <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                <FileTextOutlined className="mr-2" />
                <span className="text-gray-700">{file.name}</span>
                <Button 
                  type="link" 
                  icon={<DeleteOutlined />} 
                  onClick={resetAllStates}
                  className="ml-2"
                />
              </div>
            )}
            
            {loading && (
              <div className="mb-4 p-4 bg-blue-50 rounded-lg">
                <Spin className="mr-2" />
                <span className="text-blue-600">正在检测文档相似度...</span>
              </div>
            )}
            
            {file && !loading && results.length > 0 && (
              <div className="mb-4">
                {!selectedProjectId ? (
                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg mb-4">
                    <div className="flex items-center justify-center mb-2">
                      <ExclamationCircleOutlined className="text-gray-500 mr-2" />
                      <span className="text-gray-600 font-medium">
                        请先选择项目
                      </span>
                    </div>
                    <p className="text-gray-600 text-sm">
                      需要选择一个项目才能进行文档上传。
                    </p>
                  </div>
                ) : overallSimilarity >= 50 ? (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-lg mb-4">
                    <div className="flex items-center justify-center mb-2">
                      <ExclamationCircleOutlined className="text-red-500 mr-2" />
                      <span className="text-red-600 font-medium">
                        禁止导入 - 相似度过高 ({overallSimilarity.toFixed(1)}%)
                      </span>
                    </div>
                    <p className="text-red-600 text-sm">
                      该文档与系统中已有文档相似度过高，可能存在重复建设风险，禁止导入。
                    </p>
                  </div>
                ) : overallSimilarity >= 30 ? (
                  <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg mb-4">
                    <div className="flex items-center justify-center mb-2">
                      <ExclamationCircleOutlined className="text-orange-500 mr-2" />
                      <span className="text-orange-600 font-medium">
                        需要人工审核 - 相似度警告 ({overallSimilarity.toFixed(1)}%)
                      </span>
                    </div>
                    <p className="text-orange-600 text-sm mb-3">
                      该文档与系统中已有文档存在一定相似度，请仔细审核下方相似内容后确认上传。
                    </p>
                    <div className="flex items-center justify-center">
                      <Checkbox 
                        checked={reviewConfirmed}
                        onChange={(e) => setReviewConfirmed(e.target.checked)}
                      >
                        我已仔细审核上述相似内容，确认需要上传此文档
                      </Checkbox>
                    </div>
                  </div>
                ) : (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg mb-4">
                    <div className="flex items-center justify-center">
                      <CheckCircleOutlined className="text-green-500 mr-2" />
                      <span className="text-green-600 font-medium">
                        相似度检测通过 ({overallSimilarity.toFixed(1)}%)
                      </span>
                    </div>
                    <p className="text-green-600 text-sm mt-1">
                      该文档与系统中已有文档相似度较低，可以直接上传。
                    </p>
                  </div>
                )}
                
                <Button 
                  type="primary" 
                  size="large"
                  onClick={handleUploadToSystem}
                  loading={uploading}
                  disabled={!selectedProjectId || overallSimilarity >= 50 || (overallSimilarity >= 30 && !reviewConfirmed)}
                  className="w-full max-w-xs"
                  style={{
                    backgroundColor: !selectedProjectId || overallSimilarity >= 50 ? '#ff4d4f' : 
                                   (overallSimilarity >= 30 && !reviewConfirmed) ? '#d9d9d9' : undefined
                  }}
                >
                  {uploading ? '上传中...' : 
                   !selectedProjectId ? '请先选择项目' :
                   overallSimilarity >= 50 ? '禁止导入' :
                   overallSimilarity >= 30 && !reviewConfirmed ? '需要审核相似内容' :
                   overallSimilarity >= 30 ? '确认上传（已审核相似内容）' : '上传到系统'}
                </Button>
              </div>
            )}
            
            {uploadSuccess && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircleOutlined className="text-green-500 mr-2" />
                <span className="text-green-600">文档上传成功！</span>
              </div>
            )}
          </div>
        </Card>

      {/* 相似度检测结果显示 */}
      {results.length > 0 && (
        <Card 
          title="相似度检测结果" 
          className="mb-6"
          extra={
            !hasAiAnalysis && (
              <Button 
                type="primary"
                icon={<DiffOutlined />}
                loading={aiAnalysisLoading}
                onClick={handleGenerateAiAnalysis}
                size="small"
              >
                {aiAnalysisLoading ? '分析中...' : '生成AI智能分析'}
              </Button>
            )
          }
        >
          {results.map((result, index) => (
            <Card
              key={`result-${index}-${result.document1_name}-${result.analysis_time}`}
              type="inner"
              title={`与 ${result.document2_name} 的对比`}
              extra={
                <Space>
                  <Tag color={
                    result.is_duplicate ? 'red' : 
                    result.needs_review ? 'orange' : 'green'
                  }>
                    {result.is_duplicate ? '禁止导入' : 
                     result.needs_review ? '需要审核' : '正常'}
                  </Tag>
                  <Text strong>{result.similarity_score.toFixed(1)}%</Text>
                </Space>
              }
              className="mb-4"
            >
              {/* AI分析按钮 - 当没有AI分析时显示 */}
              {!result.ai_analysis && !aiAnalysisLoading && (
                <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg text-center">
                  <div className="mb-2">
                    <span className="text-blue-600">🤖 想要获得更详细的AI智能分析？</span>
                  </div>
                  <Button 
                    type="primary"
                    icon={<DiffOutlined />}
                    onClick={handleGenerateAiAnalysis}
                    size="large"
                  >
                    生成AI智能分析
                  </Button>
                </div>
              )}
              
              {/* AI分析加载状态 */}
              {aiAnalysisLoading && (
                <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg text-center">
                  <Spin className="mr-2" />
                  <span className="text-blue-600">AI正在分析中，请稍候...</span>
                </div>
              )}
              
              {/* 大模型分析结果 - 默认折叠 */}
              {result.ai_analysis && (
                <Collapse
                  className="mb-4"
                  items={[
                    {
                      key: 'ai-analysis',
                      label: (
                        <div className="flex items-center">
                          <span className="mr-2">🤖 AI 智能分析结果</span>
                          <Tag color={result.ai_analysis.is_duplicate ? 'red' : 'green'}>
                            {result.ai_analysis.is_duplicate ? '确认重复建设' : '非重复建设'}
                          </Tag>
                        </div>
                      ),
                      children: (
                        <Alert
                          description={
                            <div className="mt-2">
                              <div className="mb-2">
                                <strong>判断结果：</strong>
                                <Tag color={result.ai_analysis.is_duplicate ? 'red' : 'green'} className="ml-2">
                                  {result.ai_analysis.is_duplicate ? '确认重复建设' : '非重复建设'}
                                </Tag>
                              </div>
                              <div className="mb-2">
                                <strong>分析依据：</strong>
                              </div>
                              <div className="p-3 bg-gray-50 rounded text-sm">
                                {(() => {
                                  const analysisKey = `ai-analysis-${index}`;
                                  const isAnalysisExpanded = expandedContent[analysisKey];
                                  const analysisContent = result.ai_analysis.analysis || '';
                                  const shouldTruncateAnalysis = analysisContent.length > 500;
                                  
                                  return (
                                    <div>
                                      <ReactMarkdown 
                                        className="markdown-content"
                                        remarkPlugins={[remarkGfm]}
                                      >
                                        {isAnalysisExpanded || !shouldTruncateAnalysis 
                                          ? analysisContent 
                                          : analysisContent.substring(0, 500) + '...'}
                                      </ReactMarkdown>
                                      {shouldTruncateAnalysis && (
                                        <Button
                                          type="link"
                                          size="small"
                                          icon={isAnalysisExpanded ? <UpOutlined /> : <DownOutlined />}
                                          onClick={() => {
                                            setExpandedContent(prev => ({
                                              ...prev,
                                              [analysisKey]: !prev[analysisKey]
                                            }));
                                          }}
                                          className="p-0 mt-2"
                                        >
                                          {isAnalysisExpanded ? '收起' : '展开完整分析'}
                                        </Button>
                                      )}
                                    </div>
                                  );
                                })()}
                              </div>
                              <div className="text-xs text-gray-500 mt-2">
                                分析时间：{new Date(result.ai_analysis.analysis_time).toLocaleString()}
                              </div>
                            </div>
                          }
                          type={result.ai_analysis.is_duplicate ? 'warning' : 'info'}
                          showIcon
                        />
                      )
                    }
                  ]}
                />
              )}
              
              {result.similar_content.map((content, contentIndex) => (
                <div key={`content-${index}-${contentIndex}-${content.type}`} className="mb-4 p-4 bg-gray-50 rounded-lg">
                  <div className="mb-2">
                    <Tag color="blue">{content.type}</Tag>
                    <span className="ml-2 text-gray-600">
                      相似度: {content.similarity_score.toFixed(1)}%
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-gray-600 mb-2">当前文档内容：</div>
                      <div className="p-3 bg-blue-50 rounded border text-sm">
                        {(() => {
                          const key = `${index}-${contentIndex}-current`;
                          const isExpanded = expandedContent[key];
                          const shouldTruncate = content.current_content.length > 200;
                          
                          return (
                            <div>
                              <div className="whitespace-pre-wrap">
                                {isExpanded || !shouldTruncate 
                                  ? content.current_content 
                                  : truncateContent(content.current_content)}
                              </div>
                              {shouldTruncate && (
                                <Button
                                  type="link"
                                  size="small"
                                  icon={isExpanded ? <UpOutlined /> : <DownOutlined />}
                                  onClick={() => toggleContentExpansion(index, contentIndex, 'current')}
                                  className="p-0 mt-2"
                                >
                                  {isExpanded ? '收起' : '展开'}
                                </Button>
                              )}
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600 mb-2">相似文档内容：</div>
                      <div className="p-3 bg-orange-50 rounded border text-sm">
                        {(() => {
                          const key = `${index}-${contentIndex}-similar`;
                          const isExpanded = expandedContent[key];
                          const shouldTruncate = content.similar_content.length > 200;
                          
                          return (
                            <div>
                              <div className="whitespace-pre-wrap">
                                {isExpanded || !shouldTruncate 
                                  ? content.similar_content 
                                  : truncateContent(content.similar_content)}
                              </div>
                              {shouldTruncate && (
                                <Button
                                  type="link"
                                  size="small"
                                  icon={isExpanded ? <UpOutlined /> : <DownOutlined />}
                                  onClick={() => toggleContentExpansion(index, contentIndex, 'similar')}
                                  className="p-0 mt-2"
                                >
                                  {isExpanded ? '收起' : '展开'}
                                </Button>
                              )}
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </Card>
          ))}
        </Card>
      )}


    </div>
  );
};

export default SimilarityCheck;