import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Input,
  Button,
  List,
  Avatar,
  Typography,
  Space,
  Spin,
  Empty,
  Row,
  Col,
  Drawer,
  Tag,
  Tooltip,
  App,
  Select,

} from 'antd';
import {
  SendOutlined,
  UserOutlined,
  RobotOutlined,
  HistoryOutlined,
  DeleteOutlined,
  ClearOutlined,
  CopyOutlined,
  MenuOutlined,
  FolderOutlined,
} from '@ant-design/icons';
import { chatApi } from '../utils/api';
import { useChatStore } from '../utils/store';
import { formatTime, copyToClipboard, generateId } from '../utils/helpers';
import type { Message, ChatRequest } from '../types';

const { TextArea } = Input;
const { Title, Text } = Typography;
const { Option } = Select;

// 项目接口定义
interface Project {
  id: string;
  name: string;
  description?: string;
  document_count: number;
  created_time: string;
}

const ChatInterface: React.FC = () => {
  const { message } = App.useApp();
  const {
    sessions,
    currentSessionId,
    messages,
    loading,
    inputValue,
    setSessions,
    setCurrentSession,
    setMessages,
    addMessage,
    setLoading,
    setInputValue,
    clearMessages,
  } = useChatStore();

  const [historyDrawerVisible, setHistoryDrawerVisible] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');
  const [projectsLoading, setProjectsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<any>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 加载项目列表
  const loadProjects = async () => {
    try {
      setProjectsLoading(true);
      const response = await fetch('/api/projects');
      if (response.ok) {
        const data = await response.json();
        setProjects(data.projects || []);
        // 如果没有选中项目且有项目列表，默认选择第一个
        if (!selectedProjectId && data.projects && data.projects.length > 0) {
          setSelectedProjectId(data.projects[0].id);
        }
      }
    } catch (error) {
      console.error('加载项目列表失败:', error);
    } finally {
      setProjectsLoading(false);
    }
  };

  // 加载会话列表
  const loadSessions = async () => {
    try {
      const response = await chatApi.getSessions();
      setSessions(response.sessions);
    } catch (error) {
      console.error('加载会话列表失败:', error);
    }
  };

  // 加载聊天历史
  const loadChatHistory = async (sessionId: string) => {
    try {
      const response = await chatApi.getHistory(sessionId);
      setMessages(response.messages);
    } catch (error) {
      console.error('加载聊天历史失败:', error);
    }
  };

  // 处理项目切换
  const handleProjectChange = (projectId: string) => {
    setSelectedProjectId(projectId);
    // 切换项目时清空当前对话，开始新对话
    setCurrentSession(null);
    clearMessages();
  };

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim()) {
      message.warning('请输入问题');
      return;
    }

    if (!selectedProjectId) {
      message.warning('请先选择一个项目');
      return;
    }

    const question = inputValue.trim();
    setInputValue('');

    console.log('🚀 [DEBUG] handleSendMessage 开始');
    console.log('🚀 [DEBUG] 当前会话ID:', currentSessionId);
    console.log('🚀 [DEBUG] 当前消息数量:', messages.length);

    // 添加用户消息
    const userMessage: Message = {
      id: generateId(),
      type: 'user',
      content: question,
      timestamp: new Date().toISOString(),
    };
    addMessage(userMessage);
    console.log('🚀 [DEBUG] 添加用户消息后，消息数量:', messages.length + 1);

    try {
      setLoading(true);
      
      const request: ChatRequest = {
        question,
        session_id: currentSessionId || undefined,
        project_id: selectedProjectId,
      };

      console.log('🚀 [DEBUG] 发送请求:', request);
      const response = await chatApi.ask(request);
      console.log('🚀 [DEBUG] 收到响应:', { session_id: response.session_id, answer_length: response.answer.length });
      
      // 添加助手回复
      const assistantMessage: Message = {
        id: generateId(),
        type: 'assistant',
        content: response.answer,
        timestamp: new Date().toISOString(),
        sources: response.sources,
      };
      addMessage(assistantMessage);
      console.log('🚀 [DEBUG] 添加助手消息后，消息数量应为:', messages.length + 2);

      // 更新当前会话ID
      if (response.session_id) {
        if (currentSessionId === null) {
          // 新对话的第一次回复，直接设置session_id，不清空消息
          console.log('🚀 [DEBUG] 新对话设置会话ID:', response.session_id);
          setCurrentSession(response.session_id);
          loadSessions(); // 刷新会话列表
        } else if (response.session_id !== currentSessionId) {
          // 真正的会话切换，这种情况下才需要特殊处理
          console.log('🚀 [DEBUG] 会话ID变化:', currentSessionId, '->', response.session_id);
          setCurrentSession(response.session_id);
          loadSessions(); // 刷新会话列表
        } else {
          console.log('🚀 [DEBUG] 会话ID未变化，保持:', currentSessionId);
        }
      }
    } catch (error: any) {
      console.error('发送消息失败:', error);
      
      // 获取详细错误信息
      let errorMsg = '发送消息失败，请稍后重试';
      let assistantErrorMsg = '抱歉，处理您的问题时出现了错误，请稍后重试。';
      
      if (error.response?.data?.detail) {
        errorMsg = `发送失败: ${error.response.data.detail}`;
        assistantErrorMsg = `抱歉，${error.response.data.detail}`;
      } else if (error.response?.data?.error) {
        errorMsg = `发送失败: ${error.response.data.error}`;
        assistantErrorMsg = `抱歉，${error.response.data.error}`;
      } else if (error.message) {
        errorMsg = `发送失败: ${error.message}`;
        assistantErrorMsg = `抱歉，${error.message}`;
      }
      
      message.error(errorMsg);
      
      // 添加错误消息
      const errorMessage: Message = {
        id: generateId(),
        type: 'assistant',
        content: assistantErrorMsg,
        timestamp: new Date().toISOString(),
      };
      addMessage(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 切换会话
  const handleSwitchSession = async (sessionId: string) => {
    setCurrentSession(sessionId);
    await loadChatHistory(sessionId);
    setHistoryDrawerVisible(false);
  };

  // 删除会话
  const handleDeleteSession = async (sessionId: string) => {
    try {
      await chatApi.deleteSession(sessionId);
      message.success('会话删除成功');
      
      if (sessionId === currentSessionId) {
        setCurrentSession(null);
        clearMessages();
      }
      
      loadSessions();
    } catch (error) {
      console.error('删除会话失败:', error);
    }
  };

  // 清空所有会话
  const handleClearAllSessions = async () => {
    try {
      await chatApi.clearSessions();
      message.success('所有会话已清空');
      setSessions([]);
      setCurrentSession(null);
      clearMessages();
    } catch (error) {
      console.error('清空会话失败:', error);
    }
  };

  // 复制消息
  const handleCopyMessage = async (content: string) => {
    const success = await copyToClipboard(content);
    if (success) {
      message.success('已复制到剪贴板');
    } else {
      message.error('复制失败');
    }
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  useEffect(() => {
    loadProjects();
    loadSessions();
  }, []);

  useEffect(() => {
    // 当选择项目后，清空当前会话开始新对话
    if (selectedProjectId) {
      setCurrentSession(null);
      clearMessages();
    }
  }, [selectedProjectId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 调试：监控messages变化
  useEffect(() => {
    console.log('📊 [EFFECT DEBUG] messages 数组变化:', messages.length);
    console.log('📊 [EFFECT DEBUG] currentSessionId:', currentSessionId);
    if (messages.length === 0) {
      console.log('⚠️ [EFFECT DEBUG] 消息数组为空！');
      console.trace('消息数组为空的调用栈');
    }
  }, [messages, currentSessionId]);

  // 渲染消息项
  const renderMessage = (message: Message) => {
    const isUser = message.type === 'user';
    
    return (
      <div
        key={message.id}
        className={`message-item ${isUser ? 'user' : 'assistant'}`}
        style={{ marginBottom: 16 }}
      >
        <div style={{ display: 'flex', alignItems: 'flex-start', gap: 12 }}>
          {!isUser && (
            <Avatar
              icon={<RobotOutlined />}
              style={{ backgroundColor: '#1890ff', flexShrink: 0 }}
            />
          )}
          
          <div style={{ flex: 1, minWidth: 0 }}>
            <div className={`message-content ${isUser ? 'user' : 'assistant'}`}>
              <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                {message.content}
              </div>
              
              {/* 来源信息 - 已隐藏 */}
              {/* {message.sources && message.sources.length > 0 && (
                <div style={{ marginTop: 12, paddingTop: 12, borderTop: '1px solid #f0f0f0' }}>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    参考来源：
                  </Text>
                  <div style={{ marginTop: 4 }}>
                    {message.sources.map((source: Source, index: number) => (
                      <Tag key={index} style={{ marginBottom: 4 }}>
                        {source.document_name} (相似度: {(source.similarity * 100).toFixed(1)}%)
                      </Tag>
                    ))}
                  </div>
                </div>
              )} */}
            </div>
            
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginTop: 8 
            }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                {formatTime(message.timestamp, 'HH:mm:ss')}
              </Text>
              
              {!isUser && (
                <Tooltip title="复制">
                  <Button
                    type="text"
                    size="small"
                    icon={<CopyOutlined />}
                    onClick={() => handleCopyMessage(message.content)}
                  />
                </Tooltip>
              )}
            </div>
          </div>
          
          {isUser && (
            <Avatar
              icon={<UserOutlined />}
              style={{ backgroundColor: '#52c41a', flexShrink: 0 }}
            />
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="page-container">
      <Row gutter={16}>
        <Col span={24}>
          <Card
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Title level={4} style={{ margin: 0 }}>
                  智能问答对话
                </Title>
                <Space>
                  <Button
                    icon={<MenuOutlined />}
                    onClick={() => setHistoryDrawerVisible(true)}
                  >
                    会话历史
                  </Button>
                </Space>
              </div>
            }
            extra={
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <FolderOutlined style={{ color: '#1890ff' }} />
                <span style={{ fontWeight: 500 }}>项目：</span>
                <Select
                  value={selectedProjectId}
                  onChange={handleProjectChange}
                  loading={projectsLoading}
                  style={{ minWidth: 200 }}
                  placeholder="请选择项目"
                >
                  {projects.map(project => (
                    <Option key={project.id} value={project.id}>
                      <Space>
                        <FolderOutlined />
                        {project.name}
                        <Tag color="blue">{project.document_count}</Tag>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </div>
            }
            style={{ 
              height: 'calc(100vh - 200px)', 
              display: 'flex', 
              flexDirection: 'column',
              minHeight: '600px'
            }}
            styles={{ body: { flex: 1, display: 'flex', flexDirection: 'column', padding: 0 } }}
          >
            {/* 消息列表 */}
            <div
              style={{
                flex: 1,
                overflow: 'auto',
                padding: '16px 24px',
                background: '#fafafa',
                maxHeight: 'calc(100vh - 350px)',
                minHeight: '400px'
              }}
            >
              {messages.length === 0 ? (
                <Empty
                  description="开始您的第一次对话吧！"
                  style={{ marginTop: 100 }}
                />
              ) : (
                <div>
                  {messages.map(renderMessage)}
                  {loading && (
                    <div style={{ textAlign: 'center', padding: 20 }}>
                      <Spin spinning={true} tip="AI正在思考中...">
                        <div style={{ height: 40 }} />
                      </Spin>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </div>

            {/* 输入区域 */}
            <div
              style={{
                padding: '16px 24px',
                background: '#fff',
                borderTop: '1px solid #f0f0f0',
              }}
            >
              <div style={{ display: 'flex', gap: 12 }}>
                <TextArea
                  ref={inputRef}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="请输入您的问题... (Shift+Enter换行，Enter发送)"
                  autoSize={{ minRows: 1, maxRows: 4 }}
                  disabled={loading}
                />
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={handleSendMessage}
                  loading={loading}
                  disabled={!inputValue.trim() || !selectedProjectId}
                >
                  发送
                </Button>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 会话历史抽屉 */}
      <Drawer
        title="会话历史"
        placement="right"
        onClose={() => setHistoryDrawerVisible(false)}
        open={historyDrawerVisible}
        width={400}
        extra={
          <Button
            type="text"
            danger
            icon={<ClearOutlined />}
            onClick={handleClearAllSessions}
          >
            清空所有
          </Button>
        }
      >
        {sessions.length === 0 ? (
          <Empty description="暂无会话历史" />
        ) : (
          <List
            dataSource={sessions}
            renderItem={(session) => (
              <List.Item
                actions={[
                  <Tooltip title="删除会话">
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeleteSession(session.id)}
                    />
                  </Tooltip>,
                ]}
                style={{
                  cursor: 'pointer',
                  backgroundColor: session.id === currentSessionId ? '#f0f8ff' : 'transparent',
                }}
                onClick={() => handleSwitchSession(session.id)}
              >
                <List.Item.Meta
                  avatar={<Avatar icon={<HistoryOutlined />} />}
                  title={
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text ellipsis style={{ maxWidth: 200 }}>
                        {session.title || `会话 ${session.id.substring(0, 8)}`}
                      </Text>
                      {session.id === currentSessionId && (
                        <Tag color="blue">当前</Tag>
                      )}
                    </div>
                  }
                  description={
                    <div>
                      <div>消息数: {session.message_count || 0}</div>
                      <div>{formatTime(session.created_time)}</div>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </Drawer>
    </div>
  );
};

export default ChatInterface;