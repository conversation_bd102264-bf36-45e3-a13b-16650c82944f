import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Space,
  Tag,
  Typography,
  Descriptions,
  Alert,
  Spin,
  Switch,
  InputNumber,
  Form,
  App,
} from 'antd';
import {
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  DatabaseOutlined,
  CloudOutlined,
  SettingOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import { systemApi } from '../utils/api';
import { useSystemStore } from '../utils/store';
import {
  formatTime,
  getSystemStatusText,
  getSystemStatusColor,
  formatFileSize,
} from '../utils/helpers';


const { Title, Paragraph, Text } = Typography;

interface SystemSettings {
  maxFileSize: number;
  allowedFileTypes: string[];
  vectorDimension: number;
  chunkSize: number;
  chunkOverlap: number;
  autoVectorize: boolean;
}

const SystemSettings: React.FC = () => {
  const { message } = App.useApp();
  const { status, loading, setStatus, setLoading } = useSystemStore();
  const [testingConnections, setTestingConnections] = useState(false);
  const [settings, setSettings] = useState<SystemSettings>({
    maxFileSize: 50,
    allowedFileTypes: ['.docx'],
    vectorDimension: 1536,
    chunkSize: 1000,
    chunkOverlap: 200,
    autoVectorize: false,
  });
  const [form] = Form.useForm();

  // 加载系统状态
  const loadSystemStatus = async () => {
    try {
      setLoading(true);
      const response = await systemApi.getStatus();
      setStatus(response);
    } catch (error) {
      console.error('加载系统状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 测试连接
  const testConnections = async () => {
    try {
      setTestingConnections(true);
      await systemApi.testConnections();
      message.success('所有连接测试通过');
      loadSystemStatus(); // 重新加载状态
    } catch (error) {
      console.error('连接测试失败:', error);
    } finally {
      setTestingConnections(false);
    }
  };

  // 保存设置
  const handleSaveSettings = async (values: SystemSettings) => {
    try {
      // 这里应该调用保存设置的API
      // await systemApi.saveSettings(values);
      setSettings(values);
      message.success('设置保存成功');
    } catch (error) {
      console.error('保存设置失败:', error);
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'partial':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'unhealthy':
      case 'error':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <ExclamationCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  useEffect(() => {
    loadSystemStatus();
  }, []);

  useEffect(() => {
    form.setFieldsValue(settings);
  }, [settings, form]);

  if (loading && !status) {
    return (
      <div className="page-container">
        <div style={{ textAlign: 'center', padding: 100 }}>
          <Spin size="large" tip="加载系统状态中..." />
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={2} className="page-title">
          系统设置
        </Title>
        <Paragraph className="page-description">
          查看系统状态、管理系统配置和测试外部服务连接
        </Paragraph>
      </div>

      {/* 系统状态概览 */}
      <Card
        title={
          <Space>
            <SettingOutlined />
            系统状态概览
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadSystemStatus}
              loading={loading}
            >
              刷新状态
            </Button>
            <Button
              type="primary"
              icon={<CheckCircleOutlined />}
              onClick={testConnections}
              loading={testingConnections}
            >
              测试连接
            </Button>
          </Space>
        }
        style={{ marginBottom: 24 }}
      >
        {status && (
          <>
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="系统状态"
                    value={getSystemStatusText(status.status)}
                    prefix={getStatusIcon(status.status)}
                    valueStyle={{
                      color: status.status === 'healthy' ? '#3f8600' : 
                             status.status === 'partial' ? '#faad14' : '#cf1322'
                    }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="运行时间"
                    value={Math.floor((Date.now() - new Date(status.start_time).getTime()) / 3600000)}
                    suffix="小时"
                    prefix={<CheckCircleOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="文档总数"
                    value={status.documents_count}
                    prefix={<DatabaseOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="向量总数"
                    value={status.vector_count}
                    prefix={<CloudOutlined />}
                  />
                </Card>
              </Col>
            </Row>

            {/* 连接状态 */}
            <Row gutter={16}>
              <Col span={12}>
                <Card title="ChromaDB 连接状态" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>连接状态：</Text>
                      <Tag color={getSystemStatusColor(status.chroma_status)}>
                        {getStatusIcon(status.chroma_status)}
                        {getSystemStatusText(status.chroma_status)}
                      </Tag>
                    </div>
                    {status.chroma_collections && (
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Text>集合数量：</Text>
                        <Text>{status.chroma_collections}</Text>
                      </div>
                    )}
                  </Space>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="阿里云百炼 API 状态" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>连接状态：</Text>
                      <Tag color={getSystemStatusColor(status.alibaba_status)}>
                        {getStatusIcon(status.alibaba_status)}
                        {getSystemStatusText(status.alibaba_status)}
                      </Tag>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>API 版本：</Text>
                      <Text>v1.0</Text>
                    </div>
                  </Space>
                </Card>
              </Col>
            </Row>
          </>
        )}
      </Card>

      {/* 系统配置 */}
      <Card
        title="系统配置"
        style={{ marginBottom: 24 }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveSettings}
          initialValues={settings}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="最大文件大小 (MB)"
                name="maxFileSize"
                rules={[{ required: true, message: '请输入最大文件大小' }]}
              >
                <InputNumber
                  min={1}
                  max={100}
                  style={{ width: '100%' }}
                  placeholder="50"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="向量维度"
                name="vectorDimension"
                rules={[{ required: true, message: '请输入向量维度' }]}
              >
                <InputNumber
                  min={128}
                  max={4096}
                  style={{ width: '100%' }}
                  placeholder="1536"
                  disabled
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="文本块大小"
                name="chunkSize"
                rules={[{ required: true, message: '请输入文本块大小' }]}
              >
                <InputNumber
                  min={100}
                  max={2000}
                  style={{ width: '100%' }}
                  placeholder="1000"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="文本块重叠"
                name="chunkOverlap"
                rules={[{ required: true, message: '请输入文本块重叠' }]}
              >
                <InputNumber
                  min={0}
                  max={500}
                  style={{ width: '100%' }}
                  placeholder="200"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="自动向量化"
                name="autoVectorize"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
              保存配置
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 系统信息 */}
      {status && (
        <Card title="系统信息">
          <Descriptions column={2} bordered>
            <Descriptions.Item label="系统版本">v1.0.0</Descriptions.Item>
            <Descriptions.Item label="启动时间">
              {formatTime(status.start_time)}
            </Descriptions.Item>
            <Descriptions.Item label="Python 版本">3.9+</Descriptions.Item>
            <Descriptions.Item label="FastAPI 版本">0.104+</Descriptions.Item>
            <Descriptions.Item label="ChromaDB 版本">0.4+</Descriptions.Item>
            <Descriptions.Item label="支持的文件格式">
              <Space>
                {settings.allowedFileTypes.map(type => (
                  <Tag key={type}>{type}</Tag>
                ))}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="最大文件大小">
              {formatFileSize(settings.maxFileSize * 1024 * 1024)}
            </Descriptions.Item>
            <Descriptions.Item label="向量维度">
              {settings.vectorDimension}
            </Descriptions.Item>
          </Descriptions>
        </Card>
      )}

      {/* 状态警告 */}
      {status && status.status !== 'healthy' && (
        <Alert
          message="系统状态异常"
          description="检测到系统存在异常，请检查外部服务连接状态或联系管理员。"
          type="warning"
          showIcon
          style={{ marginTop: 24 }}
          action={
            <Button size="small" onClick={testConnections}>
              重新测试
            </Button>
          }
        />
      )}
    </div>
  );
};

export default SystemSettings;