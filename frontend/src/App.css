/* Tailwind CSS v3 导入 */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 暗色主题滚动条 */
[data-theme='dark'] ::-webkit-scrollbar-track {
  background: #2f2f2f;
}

[data-theme='dark'] ::-webkit-scrollbar-thumb {
  background: #6c6c6c;
}

[data-theme='dark'] ::-webkit-scrollbar-thumb:hover {
  background: #8c8c8c;
}

/* 布局样式 */
.app-layout {
  height: 100vh;
}

.app-header {
  background: #fff;
  padding: 0 24px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.app-logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.app-logo-icon {
  margin-right: 8px;
  font-size: 24px;
}

.app-sider {
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.app-content {
  margin: 0;
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
  overflow-y: auto;
}

/* 页面容器 */
.page-container {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.page-description {
  color: #8c8c8c;
  margin-top: 8px;
  margin-bottom: 0;
}

/* 卡片样式 */
.custom-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  transition: box-shadow 0.3s ease;
}

.custom-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 按钮样式 */
.action-button {
  margin-right: 8px;
}

.action-button:last-child {
  margin-right: 0;
}

/* 状态标签 */
.status-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 文件上传区域 */
.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  background: #fafafa;
  transition: border-color 0.3s ease;
}

.upload-area:hover {
  border-color: #1890ff;
}

.upload-area.dragover {
  border-color: #1890ff;
  background: #f0f8ff;
}

/* 聊天界面样式 */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;
  min-height: 0; /* 确保 flex 子元素可以收缩 */
}

.chat-input-area {
  padding: 16px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  flex-shrink: 0; /* 防止输入区域被压缩 */
}

.message-item {
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
}

.message-item.user {
  justify-content: flex-end;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  word-wrap: break-word;
}

.message-content.user {
  background: #1890ff;
  color: #fff;
}

.message-content.assistant {
  background: #fff;
  border: 1px solid #f0f0f0;
  color: #262626;
}

.message-time {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-content {
    padding: 16px;
  }
  
  .page-container {
    padding: 16px;
  }
  
  .message-content {
    max-width: 85%;
  }
}

/* 加载动画 */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px;
  color: #8c8c8c;
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

/* 工具提示 */
.tooltip-content {
  max-width: 300px;
  word-wrap: break-word;
}

/* 暗色主题适配 */
[data-theme='dark'] .app-header {
  background: #141414;
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .app-sider {
  background: #141414;
}

[data-theme='dark'] .app-content {
  background: #000;
}

[data-theme='dark'] .page-container {
  background: #141414;
  border: 1px solid #303030;
}

[data-theme='dark'] .page-title {
  color: #fff;
}

[data-theme='dark'] .upload-area {
  background: #1f1f1f;
  border-color: #434343;
}

[data-theme='dark'] .chat-messages {
  background: #1f1f1f;
}

[data-theme='dark'] .message-content.assistant {
  background: #262626;
  border-color: #434343;
  color: #fff;
}