import axios, { AxiosResponse, AxiosError } from 'axios';
import type {
  Document,
  DocumentUploadResponse,
  DocumentListResponse,
  VectorizeResponse,
  ChatRequest,
  ChatResponse,
  ChatHistoryResponse,
  SystemStatus,
  DuplicateCheckResponse,
  DocumentSummary
} from '../types';

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 0, // 移除超时限制
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    // 统一错误处理
    const errorMessage = getErrorMessage(error);
    console.error('API Error:', errorMessage);
    return Promise.reject(error);
  }
);

// 获取错误信息
function getErrorMessage(error: AxiosError): string {
  if (error.response) {
    const data = error.response.data as any;
    return data?.error || data?.detail || '服务器错误';
  } else if (error.request) {
    return '网络连接失败，请检查网络设置';
  } else {
    return error.message || '未知错误';
  }
}

// 文档相关API
export const documentApi = {
  // 上传文档
  upload: async (file: File): Promise<DocumentUploadResponse> => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post<DocumentUploadResponse>('/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 获取文档列表
  getList: async (page = 1, pageSize = 10): Promise<DocumentListResponse> => {
    const response = await api.get<DocumentListResponse>('/documents', {
      params: { page, page_size: pageSize },
    });
    return response.data;
  },

  // 获取单个文档
  getById: async (id: string): Promise<Document> => {
    const response = await api.get<Document>(`/documents/${id}`);
    return response.data;
  },

  // 向量化文档
  vectorize: async (id: string): Promise<VectorizeResponse> => {
    const response = await api.post<VectorizeResponse>(`/documents/${id}/vectorize`);
    return response.data;
  },

  // 删除文档
  delete: async (id: string): Promise<void> => {
    await api.delete(`/documents/${id}`);
  },

  // 检查文档重复性
  checkDuplicate: async (id: string): Promise<DuplicateCheckResponse> => {
    const response = await api.post<DuplicateCheckResponse>(`/documents/${id}/check-duplicate`);
    return response.data;
  },

  // 获取文档摘要
  getSummary: async (id: string): Promise<{ document_id: string; summary: DocumentSummary }> => {
    const response = await api.get(`/documents/${id}/summary`);
    return response.data;
  },
};

// 聊天相关API
export const chatApi = {
  // 发送问题
  ask: async (request: ChatRequest): Promise<ChatResponse> => {
    const response = await api.post<ChatResponse>('/chat/ask', request);
    return response.data;
  },

  // 获取聊天历史
  getHistory: async (sessionId: string, page = 1, pageSize = 50): Promise<ChatHistoryResponse> => {
    const response = await api.get<ChatHistoryResponse>(`/chat/history/${sessionId}`, {
      params: { page, page_size: pageSize },
    });
    return response.data;
  },

  // 获取会话列表
  getSessions: async (): Promise<{ sessions: any[]; total: number }> => {
    const response = await api.get('/chat/sessions');
    return response.data;
  },

  // 删除会话
  deleteSession: async (sessionId: string): Promise<void> => {
    await api.delete(`/chat/sessions/${sessionId}`);
  },

  // 清空所有会话
  clearSessions: async (): Promise<void> => {
    await api.post('/chat/sessions/clear');
  },
};

// 系统相关API
export const systemApi = {
  // 获取系统状态
  getStatus: async (): Promise<SystemStatus> => {
    const response = await api.get<SystemStatus>('/system/status');
    return response.data;
  },

  // 健康检查
  healthCheck: async (): Promise<any> => {
    const response = await api.get('/system/health');
    return response.data;
  },

  // 获取系统信息
  getInfo: async (): Promise<any> => {
    const response = await api.get('/system/info');
    return response.data;
  },

  // 测试连接
  testConnections: async (): Promise<any> => {
    const response = await api.post('/system/test/connections');
    return response.data;
  },
};

export default api;