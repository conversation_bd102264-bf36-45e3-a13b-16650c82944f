import { create } from 'zustand';
import type { Document, ChatSession, Message, SystemStatus } from '../types';

// 文档状态管理
interface DocumentState {
  documents: Document[];
  loading: boolean;
  currentPage: number;
  pageSize: number;
  total: number;
  setDocuments: (documents: Document[]) => void;
  addDocument: (document: Document) => void;
  updateDocument: (id: string, updates: Partial<Document>) => void;
  removeDocument: (id: string) => void;
  setLoading: (loading: boolean) => void;
  setPagination: (page: number, pageSize: number, total: number) => void;
}

export const useDocumentStore = create<DocumentState>((set) => ({
  documents: [],
  loading: false,
  currentPage: 1,
  pageSize: 10,
  total: 0,
  setDocuments: (documents) => set({ documents }),
  addDocument: (document) => set((state) => ({ 
    documents: [document, ...state.documents] 
  })),
  updateDocument: (id, updates) => set((state) => ({
    documents: state.documents.map(doc => 
      doc.id === id ? { ...doc, ...updates } : doc
    )
  })),
  removeDocument: (id) => set((state) => ({
    documents: state.documents.filter(doc => doc.id !== id)
  })),
  setLoading: (loading) => set({ loading }),
  setPagination: (currentPage, pageSize, total) => set({ 
    currentPage, pageSize, total 
  }),
}));

// 聊天状态管理
interface ChatState {
  sessions: ChatSession[];
  currentSessionId: string | null;
  messages: Message[];
  loading: boolean;
  inputValue: string;
  setSessions: (sessions: ChatSession[]) => void;
  setCurrentSession: (sessionId: string | null) => void;
  setMessages: (messages: Message[]) => void;
  addMessage: (message: Message) => void;
  setLoading: (loading: boolean) => void;
  setInputValue: (value: string) => void;
  clearMessages: () => void;
}

export const useChatStore = create<ChatState>((set) => ({
  sessions: [],
  currentSessionId: null,
  messages: [],
  loading: false,
  inputValue: '',
  setSessions: (sessions) => set({ sessions }),
  setCurrentSession: (sessionId) => set((state) => {
    console.log('🔄 [STORE DEBUG] setCurrentSession 调用:', state.currentSessionId, '->', sessionId);
    console.log('🔄 [STORE DEBUG] 当前消息数量:', state.messages.length);
    
    // 如果当前会话ID为null（新对话），设置新的session_id时不清空消息
    if (state.currentSessionId === null) {
      console.log('🔄 [STORE DEBUG] 新对话设置会话ID，保持消息');
      return {
        currentSessionId: sessionId,
        messages: state.messages // 保持现有消息
      };
    }
    
    // 只有在切换到真正不同的会话时才清空消息
    const shouldClearMessages = sessionId !== state.currentSessionId;
    console.log('🔄 [STORE DEBUG] 是否清空消息:', shouldClearMessages);
    
    return {
      currentSessionId: sessionId,
      messages: shouldClearMessages ? [] : state.messages
    };
  }),
  setMessages: (messages) => set({ messages }),
  addMessage: (message) => set((state) => {
    console.log('➕ [STORE DEBUG] addMessage 调用');
    console.log('➕ [STORE DEBUG] 当前消息数量:', state.messages.length);
    console.log('➕ [STORE DEBUG] 添加消息类型:', message.type);
    const newMessages = [...state.messages, message];
    console.log('➕ [STORE DEBUG] 新消息数量:', newMessages.length);
    return { messages: newMessages };
  }),
  setLoading: (loading) => set({ loading }),
  setInputValue: (inputValue) => set({ inputValue }),
  clearMessages: () => set({ messages: [] }),
}));

// 系统状态管理
interface SystemState {
  status: SystemStatus | null;
  loading: boolean;
  setStatus: (status: SystemStatus) => void;
  setLoading: (loading: boolean) => void;
}

export const useSystemStore = create<SystemState>((set) => ({
  status: null,
  loading: false,
  setStatus: (status) => set({ status }),
  setLoading: (loading) => set({ loading }),
}));

// 应用全局状态
interface AppState {
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark';
  setSidebarCollapsed: (collapsed: boolean) => void;
  setTheme: (theme: 'light' | 'dark') => void;
}

export const useAppStore = create<AppState>((set) => ({
  sidebarCollapsed: false,
  theme: 'light',
  setSidebarCollapsed: (sidebarCollapsed) => set({ sidebarCollapsed }),
  setTheme: (theme) => set({ theme }),
}));