.similarity-check-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 32px;
  text-align: center;
}

.page-title {
  color: #1890ff !important;
  margin-bottom: 8px !important;
}

.page-description {
  color: #666;
  font-size: 16px;
  margin-bottom: 0 !important;
}

/* 上传卡片样式 */
.upload-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.upload-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transform: translateY(-4px);
}

.upload-card .ant-card-head {
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  text-align: center;
}

.upload-card .ant-card-head-title {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.upload-card .ant-card-body {
  padding: 16px;
}

/* 上传拖拽区域 */
.upload-dragger {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
  border: 2px dashed #91d5ff !important;
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
  padding: 40px 20px !important;
  min-height: 200px !important;
}

.upload-dragger:hover {
  border-color: #1890ff !important;
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%) !important;
  transform: scale(1.02);
}

.upload-dragger .ant-upload-drag-icon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 20px !important;
}

.upload-dragger .ant-upload-text {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #262626 !important;
  margin-bottom: 12px !important;
}

.upload-dragger .ant-upload-hint {
  color: #595959 !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
}

.result-item {
  text-align: center;
  padding: 20px;
}

.result-label {
  font-size: 16px;
  font-weight: 500;
  color: #666;
  margin-bottom: 16px;
}

.result-value {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
}

.ant-progress-circle {
  margin: 0 auto;
}

.ant-table-tbody > tr > td {
  vertical-align: top;
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.ant-card-head-title {
  font-weight: 600;
}

.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
}

.ant-progress-line {
  margin: 0;
}

.ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

.ant-alert {
  border-radius: 6px;
}

.ant-modal-content {
  border-radius: 8px;
}

.ant-descriptions-bordered .ant-descriptions-item-label {
  background: #fafafa;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .similarity-check-container {
    padding: 16px;
  }
  
  .page-header {
    margin-bottom: 24px;
  }
  
  .result-item {
    padding: 16px;
  }
  
  .ant-col {
    margin-bottom: 16px;
  }
}

/* 暗色主题适配 */
[data-theme='dark'] .similarity-check-container {
  background: #141414;
}

[data-theme='dark'] .upload-dragger {
  background: #1f1f1f !important;
  border-color: #434343 !important;
}

[data-theme='dark'] .upload-dragger:hover {
  border-color: #1890ff !important;
  background: #111a2c !important;
}

[data-theme='dark'] .page-description {
  color: #999;
}

[data-theme='dark'] .result-label {
  color: #999;
}

/* Markdown 渲染样式 */
.markdown-content {
  line-height: 1.6;
  color: #333;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
  color: #262626;
}

.markdown-content h1 {
  font-size: 24px;
  border-bottom: 2px solid #e8e8e8;
  padding-bottom: 8px;
}

.markdown-content h2 {
  font-size: 20px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 6px;
}

.markdown-content h3 {
  font-size: 18px;
}

.markdown-content h4 {
  font-size: 16px;
}

.markdown-content p {
  margin: 8px 0;
}

.markdown-content ul,
.markdown-content ol {
  margin: 8px 0;
  padding-left: 24px;
}

.markdown-content li {
  margin: 4px 0;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  font-size: 14px;
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  text-align: left;
}

.markdown-content table th {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
}

.markdown-content table tr:nth-child(even) {
  background-color: #fafafa;
}

.markdown-content table tr:hover {
  background-color: #e6f7ff;
}

.markdown-content blockquote {
  margin: 16px 0;
  padding: 8px 16px;
  border-left: 4px solid #1890ff;
  background-color: #f0f9ff;
  color: #595959;
}

.markdown-content code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.markdown-content pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 16px 0;
}

.markdown-content pre code {
  background: none;
  padding: 0;
}

.markdown-content hr {
  border: none;
  border-top: 1px solid #e8e8e8;
  margin: 24px 0;
}

.markdown-content strong {
  font-weight: 600;
  color: #262626;
}

.markdown-content em {
  font-style: italic;
  color: #595959;
}

/* 暗色主题下的 Markdown 样式 */
[data-theme='dark'] .markdown-content {
  color: #d9d9d9;
}

[data-theme='dark'] .markdown-content h1,
[data-theme='dark'] .markdown-content h2,
[data-theme='dark'] .markdown-content h3,
[data-theme='dark'] .markdown-content h4,
[data-theme='dark'] .markdown-content h5,
[data-theme='dark'] .markdown-content h6 {
  color: #f0f0f0;
}

[data-theme='dark'] .markdown-content h1 {
  border-bottom-color: #434343;
}

[data-theme='dark'] .markdown-content h2 {
  border-bottom-color: #434343;
}

[data-theme='dark'] .markdown-content table th,
[data-theme='dark'] .markdown-content table td {
  border-color: #434343;
}

[data-theme='dark'] .markdown-content table th {
  background-color: #262626;
  color: #f0f0f0;
}

[data-theme='dark'] .markdown-content table tr:nth-child(even) {
  background-color: #1f1f1f;
}

[data-theme='dark'] .markdown-content table tr:hover {
  background-color: #111a2c;
}

[data-theme='dark'] .markdown-content blockquote {
  border-left-color: #1890ff;
  background-color: #111a2c;
  color: #bfbfbf;
}

[data-theme='dark'] .markdown-content code {
  background-color: #262626;
  color: #f0f0f0;
}

[data-theme='dark'] .markdown-content pre {
  background-color: #262626;
}

[data-theme='dark'] .markdown-content hr {
  border-top-color: #434343;
}

[data-theme='dark'] .markdown-content strong {
  color: #f0f0f0;
}

[data-theme='dark'] .markdown-content em {
  color: #bfbfbf;
}

[data-theme='dark'] .upload-dragger .ant-upload-text {
  color: #fff;
}

[data-theme='dark'] .upload-dragger .ant-upload-hint {
  color: #666;
}