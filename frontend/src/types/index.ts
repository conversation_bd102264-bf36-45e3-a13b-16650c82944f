// 文档摘要类型
export interface DocumentSummary {
  project_background: string;
  product_requirements: string;
  core_functions: string;
  target_users: string;
}

// 重复检测结果类型
export interface DuplicateDetectionResult {
  is_duplicate: boolean;
  similarity_score: number;
  duplicate_document_id?: string;
  duplicate_document_name?: string;
  similar_content: Array<{
    type: string;
    current_content: string;
    similar_content: string;
    similarity_score: number;
  }>;
  detection_time?: string;
}

// 文档相关类型
export interface Document {
  id: string;
  filename: string;
  original_name: string;
  file_path: string;
  status: 'uploading' | 'uploaded' | 'processing' | 'completed' | 'failed';
  upload_time: string;
  vectorize_time?: string;
  chunk_count: number;
  file_size: number;
  created_at: string;
  updated_at: string;
  vector_count: number;
  content?: string;
  summary?: DocumentSummary;
  duplicate_detection?: DuplicateDetectionResult;
}

export interface DocumentUploadResponse {
  success: boolean;
  document_id: string;
  message: string;
  filename: string;
  document: Document;
}

export interface DocumentListResponse {
  documents: Document[];
  total: number;
  page: number;
  page_size: number;
}

export interface VectorizeResponse {
  success: boolean;
  status: string;
  task_id: string;
  chunks_count: number;
}

// 重复检测响应类型
export interface DuplicateCheckResponse {
  success: boolean;
  document_id: string;
  is_duplicate: boolean;
  similarity_score: number;
  duplicate_document_id?: string;
  duplicate_document_name?: string;
  similar_content: Array<{
    type: string;
    current_content: string;
    similar_content: string;
    similarity_score: number;
  }>;
  message: string;
}

// 聊天相关类型
export interface Source {
  document_id: string;
  document_name: string;
  content: string;
  similarity: number;
  chunk_index: number;
}

export interface ChatRequest {
  question: string;
  session_id?: string;
  project_id?: string;
  stream?: boolean;
}

export interface ChatResponse {
  answer: string;
  sources: Source[];
  session_id: string;
  tokens_used: number;
}

export interface Message {
  id: string;
  session_id?: string;
  type: 'user' | 'assistant';
  content: string;
  sources?: Source[];
  timestamp: string;
}

export interface ChatHistoryResponse {
  messages: Message[];
  session_id: string;
  total: number;
}

export interface ChatSession {
  id: string;
  title: string;
  created_time: string;
  updated_at: string;
  message_count: number;
}

// 系统状态类型
export interface SystemStatus {
  status: 'healthy' | 'partial' | 'unhealthy';
  chroma_status: 'healthy' | 'unhealthy';
  alibaba_status: 'healthy' | 'unhealthy';
  documents_count: number;
  vector_count: number;
  chroma_collections?: number;
  start_time: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  status_code?: number;
}

// 上传文件类型
export interface UploadFile {
  uid: string;
  name: string;
  status: 'uploading' | 'done' | 'error';
  response?: DocumentUploadResponse;
  percent?: number;
}