import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, theme, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { Toaster } from 'sonner';
import { useAppStore } from './utils/store';
import Layout from './components/Layout';
import DocumentManagement from './pages/DocumentManagement';
import ChatInterface from './pages/ChatInterface';
import SystemSettings from './pages/SystemSettings';

import SelfEvaluationReview from './pages/SelfEvaluationReview';
import ProjectReview from './pages/ProjectReview';
import SimilarityCheck from './pages/SimilarityCheck';
import ProjectManagement from './pages/ProjectManagement';
import './App.css';

const App: React.FC = () => {
  const { theme: appTheme } = useAppStore();

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        algorithm: appTheme === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <AntdApp>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Navigate to="/projects" replace />} />
              <Route path="/projects" element={<ProjectManagement />} />
              <Route path="/documents" element={<DocumentManagement />} />
              <Route path="/chat" element={<ChatInterface />} />
              <Route path="/table" element={<Navigate to="/self-evaluation" replace />} />
              <Route path="/self-evaluation" element={<SelfEvaluationReview />} />
              <Route path="/project-review" element={<ProjectReview />} />
              <Route path="/similarity" element={<SimilarityCheck />} />
              <Route path="/settings" element={<SystemSettings />} />
              <Route path="*" element={<Navigate to="/projects" replace />} />
            </Routes>
          </Layout>
        </Router>
        <Toaster position="top-right" richColors />
      </AntdApp>
    </ConfigProvider>
  );
};

export default App;