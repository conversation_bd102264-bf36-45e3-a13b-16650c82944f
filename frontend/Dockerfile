# 多阶段构建
# 第一阶段：构建应用
FROM dockerpull.cn/node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置npm镜像源加速下载
RUN npm config set registry https://registry.npmmirror.com/

# 复制package文件
COPY package*.json ./

# 安装依赖（优化缓存策略）
RUN npm ci --only=production --silent && \
    npm cache clean --force

# 复制源代码
COPY . .

# 重新安装开发依赖并构建应用
RUN npm install && \
    npm run build && \
    npm prune --production

# 第二阶段：生产环境
FROM dockerpull.cn/nginx:alpine

# 复制构建产物到nginx目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]