# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/
.next/
out/

# 缓存
.cache/
.parcel-cache/
.vite/

# 环境变量
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# 测试相关
coverage/
.nyc_output/
.jest/

# Git相关
.git/
.gitignore

# Docker相关
Dockerfile*
.dockerignore
docker-compose*.yml

# 文档
*.md
README*

# 备份文件
*.bak
*.backup

# 临时文件
*.tmp
*.temp

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# ESLint
.eslintcache

# TypeScript
*.tsbuildinfo

# Storybook
.storybook/
storybook-static/

# 其他
.sass-cache/
*.css.map
*.js.map