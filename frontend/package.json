{"name": "doc-qa-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:safe": "npm install && npm run build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "check": "tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^5.2.6", "antd": "^5.12.8", "axios": "^1.6.2", "dayjs": "^1.11.10", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "react-router-dom": "^6.20.1", "remark-gfm": "^4.0.1", "sonner": "^2.0.7", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "^5.2.2", "vite": "^5.0.8"}}