#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试说明列和自评标准列关联性检查功能
"""

import asyncio
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置简单的日志记录
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 简化的测试，不依赖完整的服务
print("🔍 测试说明列和自评标准列关联性检查功能")

def test_correlation_logic():
    """
    测试关联性检查逻辑
    """
    print("\n=== 测试关联性检查逻辑 ===")
    
    # 测试数据1：有关联性的情况
    print("\n--- 测试1：有关联性的情况 ---")
    headers1 = ['一级指标', '二级指标', '自评标准', '是否符合', '说明']
    row_data1 = [
        '一、送审材料完整性',
        '上级主管部门批复',
        '各地各单位限额以上项目是否取得对口省直部门书面同意。县(区）...',
        '不符合',
        '项目已经过省政务信息中心同意建设，详见方案附件1（p245）'
    ]
    
    # 查找说明列和自评标准列的索引
    explanation_index = -1
    criteria_index = -1
    
    for i, header in enumerate(headers1):
        if '说明' in header:
            explanation_index = i
        elif '自评标准' in header:
            criteria_index = i
    
    print(f"说明列索引: {explanation_index}, 自评标准列索引: {criteria_index}")
    
    if explanation_index != -1 and criteria_index != -1:
        explanation_content = row_data1[explanation_index]
        criteria_content = row_data1[criteria_index]
        print(f"自评标准: {criteria_content[:50]}...")
        print(f"说明内容: {explanation_content}")
        print("✅ 找到说明列和自评标准列")
    else:
        print("❌ 未找到说明列或自评标准列")
    
    # 测试数据2：无关联性的情况
    print("\n--- 测试2：无关联性的情况 ---")
    headers2 = ['一级指标', '二级指标', '自评标准', '是否符合', '说明']
    row_data2 = [
        '一、送审材料完整性',
        '资金来源证明',
        '项目是否有明确的资金来源，资金是否已经到位。',
        '符合',
        '今天天气很好，阳光明媚，适合出门游玩。'
    ]
    
    explanation_content2 = row_data2[4]  # 说明列
    criteria_content2 = row_data2[2]     # 自评标准列
    print(f"自评标准: {criteria_content2}")
    print(f"说明内容: {explanation_content2}")
    print("⚠️ 这种情况应该被标记为无关联")
    
    print("\n=== 逻辑测试完成 ===")
    print("\n📝 功能已成功添加到 table_service.py 中：")
    print("   - _check_explanation_criteria_correlation 方法")
    print("   - 在 evaluate_table_row 方法中集成关联性检查")
    print("   - 当关联性不足时返回 dp_out='预警'")

if __name__ == "__main__":
    test_correlation_logic()