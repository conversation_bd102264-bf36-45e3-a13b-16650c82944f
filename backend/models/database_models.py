#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型定义
定义PostgreSQL数据库表的SQLAlchemy模型
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy import (
    Column, String, Text, Integer, BigInteger, Boolean, DateTime, Float,
    ForeignKey, Index, CheckConstraint, UniqueConstraint
)
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func
import uuid
from services.database_service import Base


class ProjectModel(Base):
    """项目模型"""
    __tablename__ = "projects"
    
    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid.uuid4()))
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    document_count: Mapped[int] = mapped_column(Integer, default=0)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    
    # 关系
    documents: Mapped[List["DocumentModel"]] = relationship("DocumentModel", back_populates="project", cascade="all, delete-orphan")
    chat_sessions: Mapped[List["ChatSessionModel"]] = relationship("ChatSessionModel", back_populates="project")
    chat_messages: Mapped[List["ChatMessageModel"]] = relationship("ChatMessageModel", back_populates="project")
    
    # 约束
    __table_args__ = (
        Index('idx_projects_name', 'name'),
        Index('idx_projects_created_at', 'created_at'),
        Index('idx_projects_is_active', 'is_active'),
        Index('idx_projects_name_active', 'name', postgresql_where=Column('is_active') == True),
    )
    
    def __repr__(self):
        return f"<Project(id={self.id}, name={self.name})>"


class DocumentModel(Base):
    """文档模型"""
    __tablename__ = "documents"
    
    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid.uuid4()))
    filename: Mapped[str] = mapped_column(String(500), nullable=False)
    original_name: Mapped[str] = mapped_column(String(500), nullable=False)
    file_path: Mapped[str] = mapped_column(Text, nullable=False)
    status: Mapped[str] = mapped_column(String(50), default='uploaded')
    upload_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    vectorize_time: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    chunk_count: Mapped[int] = mapped_column(Integer, default=0)
    file_size: Mapped[int] = mapped_column(BigInteger, default=0)
    project_id: Mapped[Optional[str]] = mapped_column(UUID(as_uuid=False), ForeignKey('projects.id', ondelete='SET NULL'), nullable=True)
    
    # 文档摘要字段
    summary_project_background: Mapped[str] = mapped_column(Text, default='')
    summary_product_requirements: Mapped[str] = mapped_column(Text, default='')
    summary_core_functions: Mapped[str] = mapped_column(Text, default='')
    summary_target_users: Mapped[str] = mapped_column(Text, default='')
    
    # 重复检测结果字段
    duplicate_is_duplicate: Mapped[bool] = mapped_column(Boolean, default=False)
    duplicate_needs_review: Mapped[bool] = mapped_column(Boolean, default=False)
    duplicate_similarity_score: Mapped[float] = mapped_column(Float, default=0.0)
    duplicate_document_id: Mapped[Optional[str]] = mapped_column(UUID(as_uuid=False), nullable=True)
    duplicate_document_name: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    duplicate_similar_content: Mapped[dict] = mapped_column(JSONB, default=lambda: [])
    duplicate_detection_time: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    duplicate_ai_analysis: Mapped[dict] = mapped_column(JSONB, default=lambda: {})
    
    # 关系
    project: Mapped[Optional["ProjectModel"]] = relationship("ProjectModel", back_populates="documents")
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint("status IN ('uploading', 'uploaded', 'processing', 'completed', 'failed')", name='ck_documents_status'),
        Index('idx_documents_project_id', 'project_id'),
        Index('idx_documents_status', 'status'),
        Index('idx_documents_upload_time', 'upload_time'),
        Index('idx_documents_filename', 'filename'),
        Index('idx_documents_duplicate_score', 'duplicate_similarity_score'),
    )
    
    def __repr__(self):
        return f"<Document(id={self.id}, filename={self.filename})>"


class ChatSessionModel(Base):
    """聊天会话模型"""
    __tablename__ = "chat_sessions"
    
    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    project_id: Mapped[Optional[str]] = mapped_column(UUID(as_uuid=False), ForeignKey('projects.id', ondelete='SET NULL'), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    
    # 关系
    project: Mapped[Optional["ProjectModel"]] = relationship("ProjectModel", back_populates="chat_sessions")
    messages: Mapped[List["ChatMessageModel"]] = relationship("ChatMessageModel", back_populates="session", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_chat_sessions_project_id', 'project_id'),
        Index('idx_chat_sessions_session_id', 'session_id'),
        Index('idx_chat_sessions_created_at', 'created_at'),
    )
    
    def __repr__(self):
        return f"<ChatSession(id={self.id}, session_id={self.session_id})>"


class ChatMessageModel(Base):
    """聊天消息模型"""
    __tablename__ = "chat_messages"
    
    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id: Mapped[str] = mapped_column(String(255), ForeignKey('chat_sessions.session_id', ondelete='CASCADE'), nullable=False)
    message_type: Mapped[str] = mapped_column(String(20), nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    sources: Mapped[dict] = mapped_column(JSONB, default=lambda: [])
    timestamp: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    project_id: Mapped[Optional[str]] = mapped_column(UUID(as_uuid=False), ForeignKey('projects.id', ondelete='SET NULL'), nullable=True)
    tokens_used: Mapped[int] = mapped_column(Integer, default=0)
    
    # 关系
    session: Mapped["ChatSessionModel"] = relationship("ChatSessionModel", back_populates="messages")
    project: Mapped[Optional["ProjectModel"]] = relationship("ProjectModel", back_populates="chat_messages")
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint("message_type IN ('user', 'assistant')", name='ck_chat_messages_type'),
        Index('idx_chat_messages_session_id', 'session_id'),
        Index('idx_chat_messages_project_id', 'project_id'),
        Index('idx_chat_messages_timestamp', 'timestamp'),
        Index('idx_chat_messages_type', 'message_type'),
    )
    
    def __repr__(self):
        return f"<ChatMessage(id={self.id}, type={self.message_type})>"


class SystemConfigModel(Base):
    """系统配置模型"""
    __tablename__ = "system_config"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    config_key: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    config_value: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<SystemConfig(key={self.config_key}, value={self.config_value})>"


class OperationLogModel(Base):
    """操作日志模型"""
    __tablename__ = "operation_logs"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    operation_type: Mapped[str] = mapped_column(String(50), nullable=False)
    table_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    record_id: Mapped[Optional[str]] = mapped_column(UUID(as_uuid=False), nullable=True)
    old_data: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    new_data: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    user_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    ip_address: Mapped[Optional[str]] = mapped_column(String(45), nullable=True)  # IPv6 support
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_operation_logs_table_record', 'table_name', 'record_id'),
        Index('idx_operation_logs_created_at', 'created_at'),
        Index('idx_operation_logs_operation_type', 'operation_type'),
    )
    
    def __repr__(self):
        return f"<OperationLog(id={self.id}, operation={self.operation_type})>"


# 数据库模型映射字典
MODEL_MAPPING = {
    'projects': ProjectModel,
    'documents': DocumentModel,
    'chat_sessions': ChatSessionModel,
    'chat_messages': ChatMessageModel,
    'system_config': SystemConfigModel,
    'operation_logs': OperationLogModel,
}


def get_model_by_table_name(table_name: str):
    """根据表名获取对应的模型类"""
    return MODEL_MAPPING.get(table_name)