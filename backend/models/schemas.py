from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum
import uuid

class DocumentStatus(str, Enum):
    UPLOADING = "uploading"
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class MessageType(str, Enum):
    USER = "user"
    ASSISTANT = "assistant"

# 项目相关模型
class Project(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    document_count: int = Field(default=0)
    is_active: bool = Field(default=True)

class ProjectCreateRequest(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)

class ProjectUpdateRequest(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)

class ProjectResponse(BaseModel):
    success: bool
    project_id: str
    message: str

class ProjectListResponse(BaseModel):
    projects: List[Project]
    total: int

# 文档摘要模型
class DocumentSummary(BaseModel):
    project_background: str = ""  # 项目背景
    product_requirements: str = ""  # 产品需求
    core_functions: str = ""  # 产品核心功能
    target_users: str = ""  # 用户群体

# 重复检测结果模型
class DuplicateDetectionResult(BaseModel):
    is_duplicate: bool = False  # 50%以上视为重复，禁止导入
    needs_review: bool = False  # 30%以上需要人工审核
    similarity_score: float = 0.0
    duplicate_document_id: Optional[str] = None
    duplicate_document_name: Optional[str] = None
    similar_content: List[Dict[str, str]] = []  # 相似内容对比
    detection_time: Optional[datetime] = None
    ai_analysis: Optional[Dict[str, Any]] = None  # 大模型分析结果

# 文档相关模型
class Document(BaseModel):
    id: str
    filename: str
    original_name: str
    file_path: str
    status: DocumentStatus
    upload_time: datetime
    vectorize_time: Optional[datetime] = None
    chunk_count: int = 0
    file_size: int = 0
    project_id: Optional[str] = None  # 项目关联
    summary: Optional[DocumentSummary] = None
    duplicate_detection: Optional[DuplicateDetectionResult] = None

class DocumentUploadResponse(BaseModel):
    success: bool
    document_id: str
    message: str
    filename: str

class DocumentListResponse(BaseModel):
    documents: List[Document]
    total: int
    page: int = 1
    page_size: int = 10

class VectorizeResponse(BaseModel):
    success: bool
    status: str
    task_id: str
    chunks_count: int

# 重复检测相关响应模型
class DuplicateCheckResponse(BaseModel):
    success: bool
    document_id: str
    is_duplicate: bool
    similarity_score: float
    duplicate_document_id: Optional[str] = None
    duplicate_document_name: Optional[str] = None
    similar_content: List[Dict[str, str]] = []
    message: str

# 聊天相关模型
class Source(BaseModel):
    document_id: str
    document_name: str
    content: str
    similarity: float
    chunk_index: int

class ChatRequest(BaseModel):
    question: str = Field(..., min_length=1, max_length=1000)
    session_id: Optional[str] = None
    project_id: Optional[str] = None  # 项目关联
    stream: bool = False

class ChatResponse(BaseModel):
    answer: str
    sources: List[Source]
    session_id: str
    tokens_used: int = 0

class Message(BaseModel):
    id: str
    session_id: str
    type: MessageType
    content: str
    sources: Optional[List[Source]] = None
    timestamp: datetime
    project_id: Optional[str] = None  # 项目关联

class ChatHistoryResponse(BaseModel):
    messages: List[Message]
    session_id: str
    total: int

# 系统状态模型
class SystemStatus(BaseModel):
    status: str
    chromadb_connected: bool
    alibaba_api_connected: bool
    documents_count: int
    vectors_count: int
    uptime: str

# 错误响应模型
class ErrorResponse(BaseModel):
    error: str
    detail: Optional[str] = None
    code: Optional[str] = None