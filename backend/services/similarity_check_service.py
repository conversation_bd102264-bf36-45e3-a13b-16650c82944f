import hashlib
import re
import jieba
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from loguru import logger
from docx import Document
import pymupdf  # PyMuPDF for PDF processing
from sklearn.metrics.pairwise import cosine_similarity
import asyncio

from services.alibaba_client import alibaba_client
from services.chroma_client import ChromaClient
from app.config import settings

class SimilarityCheckService:
    def __init__(self):
        self.chroma_client = ChromaClient()
        # 使用默认集合，与文档管理系统保持一致
        self.collection_name = settings.chromadb_collection_name
        
        # Facet 关键词映射
        self.facet_keywords = {
            "project_background": ["背景", "现状", "目标", "痛点", "起因", "环境", "前提", "问题", "挑战"],
            "product_requirements": ["需求", "约束", "PRD", "边界", "验收", "要求", "规范", "标准", "条件"],
            "core_features": ["功能", "模块", "接口", "流程", "能力", "特性", "组件", "服务", "系统"],
            "user_segments": ["用户", "画像", "角色", "行业", "场景", "客户", "群体", "对象", "受众"]
        }
        
        # Facet 查询描述
        self.facet_descriptions = {
            "project_background": "项目起因、行业背景、现状问题、目标与范围",
            "product_requirements": "产品需求、功能要求、约束条件、验收标准",
            "core_features": "核心功能、主要模块、技术能力、系统特性",
            "user_segments": "目标用户、用户画像、使用场景、行业领域"
        }
        
        # 初始化jieba分词
        jieba.initialize()
        
    async def initialize(self):
        """初始化服务"""
        try:
            # 初始化ChromaDB客户端
            await self.chroma_client.initialize()
            
            # 使用默认集合，与文档管理系统共享数据
            if not self.chroma_client.client:
                logger.warning("ChromaDB客户端未初始化")
                return False
                
            # 直接使用ChromaClient已经初始化的集合
            self.collection = self.chroma_client.collection
            
            if self.collection:
                logger.info(f"成功连接到默认集合: {self.collection_name}")
                return True
            else:
                logger.error("无法获取默认ChromaDB集合")
                return False
            
        except Exception as e:
            logger.error(f"相似度检验服务初始化失败: {e}")
            return False
    
    def extract_text_from_file(self, file_path: str) -> str:
        """从文件中提取文本内容"""
        try:
            if file_path.lower().endswith('.docx'):
                return self._extract_from_docx(file_path)
            elif file_path.lower().endswith('.pdf'):
                return self._extract_from_pdf(file_path)
            else:
                raise ValueError(f"不支持的文件格式: {file_path}")
        except Exception as e:
            logger.error(f"文件文本提取失败: {e}")
            raise
    
    def _extract_from_docx(self, file_path: str) -> str:
        """从DOCX文件提取文本"""
        doc = Document(file_path)
        text_content = []
        
        # 提取段落文本
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text_content.append(paragraph.text.strip())
        
        # 提取表格文本
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text.strip())
                if row_text:
                    text_content.append(" | ".join(row_text))
        
        return "\n".join(text_content)
    
    def _extract_from_pdf(self, file_path: str) -> str:
        """从PDF文件提取文本"""
        doc = pymupdf.open(file_path)
        text_content = []
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text = page.get_text()
            if text.strip():
                text_content.append(text.strip())
        
        doc.close()
        return "\n".join(text_content)
    
    def split_document_hierarchical(self, text: str, doc_id: str, doc_title: str) -> List[Dict[str, Any]]:
        """按标题层级优先，再做滑窗切分文档"""
        try:
            chunks = []
            
            # 首先按标题层级分割
            sections = self._split_by_headings(text)
            
            for section in sections:
                section_chunks = self._sliding_window_split(
                    section['content'], 
                    section['heading_path'],
                    doc_id,
                    doc_title,
                    section.get('page', 1)
                )
                chunks.extend(section_chunks)
            
            logger.info(f"文档 {doc_title} 切分完成，共 {len(chunks)} 个块")
            return chunks
            
        except Exception as e:
            logger.error(f"文档切分失败: {e}")
            raise
    
    def _split_by_headings(self, text: str) -> List[Dict[str, Any]]:
        """按标题层级分割文本"""
        # 标题模式
        heading_patterns = [
            r'^\s*([一二三四五六七八九十]+[、.]\s*.+)$',  # 中文数字标题
            r'^\s*(\d+[、.]\s*.+)$',  # 阿拉伯数字标题
            r'^\s*([第][一二三四五六七八九十]+[章节部分][、.]?\s*.+)$',  # 第X章
            r'^\s*(Chapter\s+\d+.*)$',  # 英文章节
            r'^\s*([A-Z][、.]\s*.+)$',  # 字母标题
            r'^\s*(项目背景|产品需求|核心功能|用户群体|技术方案|实施计划).*$',  # 关键词标题
        ]
        
        lines = text.split('\n')
        sections = []
        current_section = {
            'heading_path': '文档开始',
            'content': '',
            'page': 1
        }
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否是标题
            is_heading = False
            for pattern in heading_patterns:
                if re.match(pattern, line, re.IGNORECASE):
                    # 保存当前章节
                    if current_section['content'].strip():
                        sections.append(current_section)
                    
                    # 开始新章节
                    current_section = {
                        'heading_path': line,
                        'content': '',
                        'page': 1  # 简化处理，实际应该从PDF中获取页码
                    }
                    is_heading = True
                    break
            
            if not is_heading:
                current_section['content'] += line + '\n'
        
        # 添加最后一个章节
        if current_section['content'].strip():
            sections.append(current_section)
        
        # 如果没有找到标题，返回整个文档作为一个章节
        if len(sections) == 0:
            sections = [{
                'heading_path': '完整文档',
                'content': text,
                'page': 1
            }]
        
        return sections
    
    def _sliding_window_split(self, text: str, heading_path: str, doc_id: str, doc_title: str, page: int) -> List[Dict[str, Any]]:
        """滑窗切分文本（400-800字符，overlap 50-100）"""
        chunks = []
        chunk_size = 600  # 中间值
        overlap = 75      # 中间值
        
        start = 0
        chunk_index = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # 如果不是最后一块，尝试在句号处分割
            if end < len(text):
                chunk_content = text[start:end]
                last_period = chunk_content.rfind('。')
                if last_period > chunk_size // 2:
                    end = start + last_period + 1
            
            chunk_content = text[start:end].strip()
            
            if chunk_content:
                # 自动打标
                facet_candidates = self._auto_tag_facets(chunk_content)
                
                # 计算内容哈希
                content_hash = self._calculate_content_hash(chunk_content)
                
                chunk = {
                    'doc_id': doc_id,
                    'doc_title': doc_title,
                    'heading_path': heading_path,
                    'page': page,
                    'start': start,
                    'end': end,
                    'content': chunk_content,
                    'facet_candidates': facet_candidates,
                    'content_hash': content_hash,
                    'boost': self._calculate_boost(chunk_content, facet_candidates),
                    'created_at': datetime.now().isoformat(),
                    'chunk_index': chunk_index
                }
                
                chunks.append(chunk)
                chunk_index += 1
            
            # 移动窗口
            start = end - overlap if end < len(text) else len(text)
        
        return chunks
    
    def _auto_tag_facets(self, content: str) -> List[str]:
        """自动打标，识别内容属于哪些facet"""
        facets = []
        content_lower = content.lower()
        
        for facet, keywords in self.facet_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in content_lower:
                    score += 1
            
            # 如果匹配到关键词，添加到候选facet
            if score > 0:
                facets.append(facet)
        
        # 如果没有匹配到任何facet，默认为通用内容
        if not facets:
            facets = ['general']
        
        return facets
    
    def _calculate_content_hash(self, content: str) -> str:
        """计算内容哈希用于去重"""
        # 标准化文本：去除空白字符、标点符号
        normalized = re.sub(r'[\s\p{P}]+', '', content)
        return hashlib.sha256(normalized.encode('utf-8')).hexdigest()
    
    def _calculate_boost(self, content: str, facets: List[str]) -> float:
        """计算内容权重提升值"""
        boost = 1.0
        
        # 根据内容长度调整权重
        if len(content) > 500:
            boost += 0.1
        
        # 根据facet数量调整权重
        if len(facets) > 1:
            boost += 0.05 * len(facets)
        
        # 根据关键词密度调整权重
        keyword_count = sum(len(self.facet_keywords[facet]) for facet in facets if facet in self.facet_keywords)
        if keyword_count > 5:
            boost += 0.1
        
        return min(boost, 2.0)  # 最大权重不超过2.0
    
    async def process_and_store_document(self, doc_id: str, file_path: str, doc_title: str, project_id: Optional[str] = None) -> Dict[str, Any]:
        """处理文档并存储到向量数据库"""
        try:
            logger.info(f"开始处理文档: {doc_title}")
            
            # 提取文本
            text_content = self.extract_text_from_file(file_path)
            
            # 切分文档
            chunks = self.split_document_hierarchical(text_content, doc_id, doc_title)
            
            # 去重处理
            unique_chunks = await self._deduplicate_chunks(chunks)
            
            # 向量化
            texts = [chunk['content'] for chunk in unique_chunks]
            embeddings = await alibaba_client.get_embeddings(texts)
            
            # 准备元数据
            metadatas = []
            ids = []
            
            for i, chunk in enumerate(unique_chunks):
                metadata = {
                    'doc_id': chunk['doc_id'],
                    'doc_title': chunk['doc_title'],
                    'project_id': project_id or "default",
                    'heading_path': chunk['heading_path'],
                    'page': chunk['page'],
                    'start': chunk['start'],
                    'end': chunk['end'],
                    'facet_candidates': chunk['facet_candidates'],
                    'content_hash': chunk['content_hash'],
                    'boost': chunk['boost'],
                    'created_at': chunk['created_at'],
                    'chunk_index': chunk['chunk_index']
                }
                metadatas.append(metadata)
                ids.append(f"{doc_id}_{chunk['chunk_index']}")
            
            # 存储到ChromaDB
            if self.collection:
                self.collection.add(
                    embeddings=embeddings,
                    documents=texts,
                    metadatas=metadatas,
                    ids=ids
                )
            
            result = {
                'doc_id': doc_id,
                'doc_title': doc_title,
                'total_chunks': len(chunks),
                'unique_chunks': len(unique_chunks),
                'processed_at': datetime.now().isoformat()
            }
            
            logger.info(f"文档处理完成: {doc_title}, 共 {len(unique_chunks)} 个唯一块")
            return result
            
        except Exception as e:
            logger.error(f"文档处理失败: {e}")
            raise
    
    async def _deduplicate_chunks(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重处理"""
        unique_chunks = []
        seen_hashes = set()
        
        # 精确去重
        for chunk in chunks:
            content_hash = chunk['content_hash']
            if content_hash not in seen_hashes:
                seen_hashes.add(content_hash)
                unique_chunks.append(chunk)
        
        # 语义去重（简化版本，实际可以使用更复杂的算法）
        if len(unique_chunks) > 1:
            texts = [chunk['content'] for chunk in unique_chunks]
            embeddings = await alibaba_client.get_embeddings(texts)
            
            final_chunks = []
            for i, chunk in enumerate(unique_chunks):
                is_duplicate = False
                
                for j, existing_chunk in enumerate(final_chunks):
                    if j < len(embeddings) and i < len(embeddings):
                        similarity = cosine_similarity(
                            [embeddings[i]], 
                            [embeddings[j]]
                        )[0][0]
                        
                        if similarity > 0.95:
                            # 降低权重而不是完全删除
                            chunk['boost'] *= 0.5
                            is_duplicate = True
                            break
                
                if not is_duplicate or chunk['boost'] > 0.1:
                    final_chunks.append(chunk)
            
            return final_chunks
        
        return unique_chunks
    
    async def similarity_search(self, facet: str, user_query: str, top_k: int = 20, project_id: Optional[str] = None) -> Dict[str, Any]:
        """相似度检索"""
        try:
            logger.info(f"开始相似度检索: facet={facet}, query={user_query[:50]}...")
            
            # 生成查询向量
            query_embedding = await alibaba_client.get_embeddings([user_query])
            if not query_embedding:
                raise ValueError("查询向量生成失败")
            
            # 构建facet查询描述
            if facet in self.facet_descriptions:
                enhanced_query = f"{self.facet_descriptions[facet]}: {user_query}"
            else:
                enhanced_query = user_query
            
            # 在ChromaDB中检索
            if not self.collection:
                raise ValueError("ChromaDB集合未初始化")
            
            # 构建过滤条件
            where_filter = {}
            if project_id:
                where_filter["project_id"] = project_id
            if facet != 'all':
                if where_filter:
                    where_filter = {
                        "$and": [
                            {"project_id": project_id} if project_id else {},
                            {"facet_candidates": {"$contains": facet}}
                        ]
                    }
                else:
                    where_filter["facet_candidates"] = {"$contains": facet}
            
            # 首先尝试使用过滤条件
            results = None
            if where_filter:
                try:
                    results = self.collection.query(
                        query_embeddings=query_embedding,
                        n_results=min(50, top_k * 3),
                        where=where_filter,
                        include=["documents", "metadatas", "distances"]
                    )
                    # 如果没有结果，回退到项目过滤或全量搜索
                    if not results or not results["ids"] or not results["ids"][0]:
                        logger.info(f"使用完整过滤条件未找到结果，尝试简化搜索")
                        if project_id:
                            # 只使用项目过滤
                            results = self.collection.query(
                                query_embeddings=query_embedding,
                                n_results=min(50, top_k * 3),
                                where={"project_id": project_id},
                                include=["documents", "metadatas", "distances"]
                            )
                        if not results or not results["ids"] or not results["ids"][0]:
                            results = None
                except Exception as e:
                    logger.warning(f"过滤查询失败，回退到全量搜索: {e}")
                    results = None
            
            # 如果过滤查询失败或无结果，使用全量查询
            if results is None:
                results = self.collection.query(
                    query_embeddings=query_embedding,
                    n_results=min(50, top_k * 3),  # 召回更多候选
                    include=["documents", "metadatas", "distances"]
                )
            
            if not results or not results["ids"] or not results["ids"][0]:
                logger.warning("ChromaDB中未找到任何文档")
                return {
                    'candidates': [],
                    'ai_analysis': None,
                    'total_found': 0
                }
            
            # 处理检索结果
            candidates = []
            for i in range(len(results["ids"][0])):
                candidate = {
                    'id': results["ids"][0][i],
                    'content': results["documents"][0][i],
                    'metadata': results["metadatas"][0][i],
                    'cosine_distance': results["distances"][0][i],
                    'cosine_similarity': 1 - results["distances"][0][i]
                }
                candidates.append(candidate)
            
            # 重排序（简化版本，实际可以集成bge-reranker-v2-m3）
            reranked_candidates = await self._rerank_candidates(user_query, candidates)
            
            # 综合打分
            final_candidates = self._calculate_final_scores(reranked_candidates)
            
            # 取前K个结果
            top_candidates = final_candidates[:top_k]
            
            # 大模型分析
            ai_analysis = await self._generate_ai_analysis(user_query, facet, top_candidates)
            
            result = {
                'candidates': top_candidates,
                'ai_analysis': ai_analysis,
                'total_found': len(results["ids"][0]),
                'facet': facet,
                'query': user_query
            }
            
            logger.info(f"相似度检索完成，返回 {len(top_candidates)} 个结果")
            return result
            
        except Exception as e:
            logger.error(f"相似度检索失败: {e}")
            raise
    
    async def _rerank_candidates(self, query: str, candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """重排序候选结果（简化版本）"""
        # 这里可以集成bge-reranker-v2-m3，目前使用简化的重排序逻辑
        for candidate in candidates:
            # 基于关键词匹配的简单重排序分数
            content = candidate['content'].lower()
            query_words = jieba.lcut(query.lower())
            
            match_score = 0
            for word in query_words:
                if word in content:
                    match_score += 1
            
            candidate['rerank_score'] = match_score / len(query_words) if query_words else 0
        
        return candidates
    
    def _calculate_final_scores(self, candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """计算最终综合得分"""
        for candidate in candidates:
            cosine_score = candidate['cosine_similarity']
            rerank_score = candidate.get('rerank_score', 0)
            boost = candidate['metadata'].get('boost', 1.0)
            
            # 综合打分：0.4 * cosine + 0.5 * rerank + 0.1 * boost
            final_score = 0.4 * cosine_score + 0.5 * rerank_score + 0.1 * (boost - 1.0)
            candidate['final_score'] = final_score
        
        # 按最终得分排序
        candidates.sort(key=lambda x: x['final_score'], reverse=True)
        return candidates
    
    async def _generate_ai_analysis(self, query: str, facet: str, candidates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成AI分析结果"""
        try:
            if not candidates:
                return None
            
            # 构建分析提示词
            system_prompt = """
你是一个专业的文档分析助手。请分析用户查询与检索到的文档段落的相似度和相关性。

请按照以下JSON格式输出分析结果：
{
    "doc_id": "最相关文档的ID",
    "coverage": "覆盖度评分(0-1)",
    "clarity": "清晰度评分(0-1)", 
    "actionability": "可操作性评分(0-1)",
    "conflicts": "冲突或矛盾点",
    "gaps": "缺失或不足之处",
    "best_for": "最适合的应用场景",
    "rationale": "分析理由和证据"
}

要求：
1. 必须基于提供的文档内容进行分析，不允许虚构
2. 提供具体的证据片段和定位信息
3. 评分要客观准确
4. 分析要详细且有价值
            """
            
            # 构建候选文档信息
            candidates_info = []
            for i, candidate in enumerate(candidates[:5]):  # 只分析前5个
                info = f"""
文档{i+1}:
- ID: {candidate['id']}
- 文档标题: {candidate['metadata'].get('doc_title', '未知')}
- 章节路径: {candidate['metadata'].get('heading_path', '未知')}
- 相似度得分: {candidate['final_score']:.3f}
- 内容: {candidate['content'][:300]}...
                """
                candidates_info.append(info)
            
            user_prompt = f"""
用户查询: {query}
查询维度: {facet}

检索到的相关文档段落:
{chr(10).join(candidates_info)}

请分析这些文档段落与用户查询的相关性，并按照要求的JSON格式输出分析结果。
            """
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # 调用大模型
            response = await alibaba_client.chat_completion(messages)
            
            # 尝试解析JSON响应
            try:
                import json
                # 提取JSON部分
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = response[json_start:json_end]
                    analysis = json.loads(json_str)
                else:
                    # 如果无法解析JSON，返回原始响应
                    analysis = {
                        "doc_id": candidates[0]['id'] if candidates else "unknown",
                        "coverage": 0.5,
                        "clarity": 0.5,
                        "actionability": 0.5,
                        "conflicts": "无法解析AI响应",
                        "gaps": "响应格式错误",
                        "best_for": "未知",
                        "rationale": response
                    }
            except Exception as parse_error:
                logger.warning(f"AI响应解析失败: {parse_error}")
                analysis = {
                    "doc_id": candidates[0]['id'] if candidates else "unknown",
                    "coverage": 0.5,
                    "clarity": 0.5,
                    "actionability": 0.5,
                    "conflicts": "响应解析失败",
                    "gaps": "无法解析AI分析",
                    "best_for": "未知",
                    "rationale": response[:500] + "..."
                }
            
            return analysis
            
        except Exception as e:
            logger.error(f"AI分析生成失败: {e}")
            return {
                "doc_id": "error",
                "coverage": 0.0,
                "clarity": 0.0,
                "actionability": 0.0,
                "conflicts": "AI分析失败",
                "gaps": str(e),
                "best_for": "错误处理",
                "rationale": f"分析过程中发生错误: {str(e)}"
            }

# 创建全局服务实例
similarity_check_service = SimilarityCheckService()