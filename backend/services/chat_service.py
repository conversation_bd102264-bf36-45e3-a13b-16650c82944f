import uuid
import re
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger
from models.schemas import (
    ChatRequest, ChatResponse, Message, MessageType, 
    Source, ChatHistoryResponse
)
from services.alibaba_client import alibaba_client
from services.chroma_client import chroma_client

class ChatService:
    def __init__(self):
        self.sessions_db = {}  # 简单的内存存储，实际项目中应使用数据库
        self.messages_db = {}  # 存储消息
        
        # 文档名称别名映射
        self.document_aliases = {
            "附件1": ["附件1", "附件一", "attachment1", "附件1数字化项目技术方案"],
            "附件2": ["附件2", "附件二", "attachment2", "附件2抚州市数字化项目自评表"],
            "技术方案": ["技术方案", "数字化项目技术方案", "智慧审批系统"],
            "自评表": ["自评表", "数字化项目自评表", "抚州市数字化项目自评表"]
        }
    
    def _extract_document_references(self, question: str) -> List[str]:
        """
        从用户问题中提取文档引用
        """
        referenced_docs = []
        question_lower = question.lower()
        
        # 检查每个别名组
        for doc_key, aliases in self.document_aliases.items():
            for alias in aliases:
                if alias.lower() in question_lower:
                    referenced_docs.append(doc_key)
                    logger.info(f"🎯 检测到文档引用: '{alias}' -> {doc_key}")
                    break
        
        # 使用正则表达式检测更多模式
        patterns = [
            r'附件\s*[1-9一二三四五六七八九十]',  # 附件1, 附件一等
            r'attachment\s*[1-9]',  # attachment1等
            r'文档\s*[1-9一二三四五六七八九十]',  # 文档1等
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, question, re.IGNORECASE)
            for match in matches:
                logger.info(f"🔍 正则匹配到文档引用: '{match}'")
                # 简单映射
                if '1' in match or '一' in match:
                    if "附件1" not in referenced_docs:
                        referenced_docs.append("附件1")
                elif '2' in match or '二' in match:
                    if "附件2" not in referenced_docs:
                        referenced_docs.append("附件2")
        
        return list(set(referenced_docs))  # 去重
    
    async def _search_documents_by_name(self, doc_references: List[str]) -> List[Dict[str, Any]]:
        """
        根据文档引用搜索特定文档
        """
        if not doc_references:
            return []
        
        try:
            # 获取所有文档信息
            all_docs = await chroma_client.get_all_documents()
            matched_docs = []
            
            for doc_ref in doc_references:
                aliases = self.document_aliases.get(doc_ref, [doc_ref])
                
                for doc_id, doc_info in all_docs.items():
                    doc_name = doc_info.get('document_name', '')
                    
                    # 检查文档名是否匹配任何别名
                    for alias in aliases:
                        if alias.lower() in doc_name.lower():
                            matched_docs.append({
                                'document_id': doc_id,
                                'document_name': doc_name,
                                'reference': doc_ref,
                                'matched_alias': alias
                            })
                            logger.info(f"✅ 找到匹配文档: {doc_ref} -> {doc_name}")
                            break
            
            return matched_docs
            
        except Exception as e:
            logger.error(f"搜索文档失败: {e}")
            return []
    
    async def process_question(self, request: ChatRequest) -> ChatResponse:
        """
        处理用户问题
        """
        try:
            # 获取或创建会话ID
            session_id = request.session_id or str(uuid.uuid4())
            
            # 确保会话存在
            if session_id not in self.sessions_db:
                self.sessions_db[session_id] = {
                    "id": session_id,
                    "title": self._generate_session_title(request.question),
                    "created_time": datetime.now(),
                    "last_active_time": datetime.now(),
                    "message_count": 0
                }
                self.messages_db[session_id] = []
            
            # 更新会话活跃时间
            self.sessions_db[session_id]["last_active_time"] = datetime.now()
            
            # 保存用户消息
            user_message = Message(
                id=str(uuid.uuid4()),
                session_id=session_id,
                type=MessageType.USER,
                content=request.question,
                timestamp=datetime.now(),
                project_id=request.project_id
            )
            self.messages_db[session_id].append(user_message)
            
            # 获取问题的向量化结果
            logger.info(f"开始处理问题: {request.question[:100]}...")
            question_embeddings = await alibaba_client.get_embeddings([request.question])
            question_embedding = question_embeddings[0]
            logger.info(f"问题向量化完成，向量维度: {len(question_embedding)}")
            
            # 检测用户是否提到了特定文档
            doc_references = self._extract_document_references(request.question)
            matched_docs = await self._search_documents_by_name(doc_references)
            
            # 搜索相关文档
            logger.info(f"🔍 开始向量数据库检索，查询向量维度: {len(question_embedding)}")
            
            # 构建搜索过滤条件
            search_filter = {}
            if request.project_id:
                search_filter["project_id"] = request.project_id
                logger.info(f"🎯 限定在项目 {request.project_id} 内搜索")
            
            # 如果用户提到了特定文档，优先搜索这些文档
            if matched_docs:
                logger.info(f"🎯 用户提到了特定文档，优先搜索: {[doc['reference'] for doc in matched_docs]}")
                
                # 为每个匹配的文档进行向量搜索
                all_ids, all_documents, all_metadatas, all_distances = [], [], [], []
                
                for matched_doc in matched_docs:
                    doc_id = matched_doc['document_id']
                    logger.info(f"🔍 在文档 '{matched_doc['document_name']}' 中搜索...")
                    
                    # 合并文档ID和项目ID过滤条件
                    doc_filter = {"document_id": doc_id}
                    if request.project_id:
                        doc_filter["project_id"] = request.project_id
                    
                    ids, documents, metadatas, distances = await chroma_client.search_similar(
                        query_embedding=question_embedding,
                        n_results=10,  # 增加搜索结果数量
                        where=doc_filter
                    )
                    
                    all_ids.extend(ids)
                    all_documents.extend(documents)
                    all_metadatas.extend(metadatas)
                    all_distances.extend(distances)
                    
                    logger.info(f"📊 在 '{matched_doc['document_name']}' 中找到 {len(documents)} 个片段")
                
                # 如果特定文档搜索结果不足，补充全局搜索
                if len(all_documents) < 3:
                    logger.info(f"🔍 特定文档搜索结果不足，补充全局搜索...")
                    global_ids, global_documents, global_metadatas, global_distances = await chroma_client.search_similar(
                        query_embedding=question_embedding,
                        n_results=5,
                        where=search_filter if search_filter else None
                    )
                    
                    # 合并结果，去重
                    for i, global_id in enumerate(global_ids):
                        if global_id not in all_ids:
                            all_ids.append(global_id)
                            all_documents.append(global_documents[i])
                            all_metadatas.append(global_metadatas[i])
                            all_distances.append(global_distances[i])
                
                ids, documents, metadatas, distances = all_ids, all_documents, all_metadatas, all_distances
                
            else:
                # 常规向量搜索
                ids, documents, metadatas, distances = await chroma_client.search_similar(
                    query_embedding=question_embedding,
                    n_results=5,
                    where=search_filter if search_filter else None
                )
            
            logger.info(f"📊 ChromaDB检索完成，找到 {len(documents)} 个候选文档片段")
            
            # 记录检索结果详情
            if documents:
                logger.info(f"📋 检索结果详情:")
                for i, (doc_id, distance, metadata) in enumerate(zip(ids, distances, metadatas)):
                    doc_name = metadata.get('document_name', '未知文档')
                    chunk_idx = metadata.get('chunk_index', 0)
                    logger.info(f"  片段{i+1}: 文档={doc_name}, 块索引={chunk_idx}, 距离={distance:.4f}, ID={doc_id}")
            else:
                logger.warning("⚠️ ChromaDB未返回任何文档片段")
            
            # 构建上下文和来源信息
            context_parts = []
            sources = []
            
            logger.info(f"🔧 开始构建上下文，相似度阈值: 0.2")
            
            for i, (doc_id, doc_content, metadata, distance) in enumerate(zip(ids, documents, metadatas, distances)):
                # 计算相似度分数 (距离越小，相似度越高)
                similarity = max(0, 1 - distance)
                
                logger.info(f"文档片段 {i+1}: 相似度={similarity:.3f}, 距离={distance:.3f}, 文档={metadata.get('document_name', '未知')}")
                
                if similarity > 0.2:  # 降低相似度阈值，让更多文档能被检索到
                    context_parts.append(f"文档片段{i+1}：{doc_content}")
                    
                    source = Source(
                        document_id=metadata.get("document_id", ""),
                        document_name=metadata.get("document_name", "未知文档"),
                        content=doc_content[:200] + "..." if len(doc_content) > 200 else doc_content,
                        similarity=similarity,
                        chunk_index=metadata.get("chunk_index", 0)
                    )
                    sources.append(source)
                    logger.info(f"✅ 采用片段{i+1}: 相似度={similarity:.3f}, 距离={distance:.4f}, 来源={metadata.get('document_name', '未知')}")
                else:
                    logger.info(f"❌ 跳过片段{i+1}: 相似度={similarity:.3f} 低于阈值0.2")
            
            # 构建上下文
            context = "\n\n".join(context_parts) if context_parts else None
            context_length = len(context) if context else 0
            logger.info(f"📝 上下文构建完成，使用了 {len(context_parts)} 个文档片段，总长度: {context_length} 字符")
            
            # 构建对话历史
            chat_history = []
            recent_messages = self.messages_db[session_id][-10:]  # 最近10条消息
            
            for msg in recent_messages:
                if msg.type == MessageType.USER:
                    chat_history.append({"role": "user", "content": msg.content})
                elif msg.type == MessageType.ASSISTANT:
                    chat_history.append({"role": "assistant", "content": msg.content})
            
            # 添加当前问题
            chat_history.append({"role": "user", "content": request.question})
            
            # 生成回答，无论是否找到相关文档都让AI回答
            if context_parts:
                logger.info(f"📚 最终使用的文档来源: {[source.document_name for source in sources]}")
                # 有相关文档时，使用文档内容生成回答
                answer = await alibaba_client.chat_completion(
                    messages=chat_history,
                    context=context,
                    document_references=doc_references if doc_references else None
                )
            else:
                logger.warning(f"⚠️ 没有找到相似度足够高的文档片段，但仍让AI尝试回答")
                # 没有相关文档时，让AI基于通用知识回答
                answer = await alibaba_client.chat_completion(
                    messages=chat_history,
                    context=None,
                    document_references=None
                )
            
            # 保存助手回答
            assistant_message = Message(
                id=str(uuid.uuid4()),
                session_id=session_id,
                type=MessageType.ASSISTANT,
                content=answer,
                sources=sources,
                timestamp=datetime.now(),
                project_id=request.project_id
            )
            self.messages_db[session_id].append(assistant_message)
            
            # 更新会话消息计数
            self.sessions_db[session_id]["message_count"] = len(self.messages_db[session_id])
            
            logger.info(f"问答处理完成，会话: {session_id}，找到 {len(sources)} 个相关文档片段")
            
            return ChatResponse(
                answer=answer,
                sources=sources,
                session_id=session_id,
                tokens_used=len(answer)  # 简单估算
            )
            
        except Exception as e:
            logger.error(f"问答处理失败: {e}")
            raise Exception(f"问答处理失败: {str(e)}")
    
    async def get_chat_history(self, session_id: str, page: int = 1, page_size: int = 50) -> ChatHistoryResponse:
        """
        获取聊天历史
        """
        try:
            if session_id not in self.messages_db:
                return ChatHistoryResponse(
                    messages=[],
                    session_id=session_id,
                    total=0
                )
            
            messages = self.messages_db[session_id]
            messages.sort(key=lambda x: x.timestamp)
            
            total = len(messages)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            
            page_messages = messages[start_idx:end_idx]
            
            return ChatHistoryResponse(
                messages=page_messages,
                session_id=session_id,
                total=total
            )
            
        except Exception as e:
            logger.error(f"获取聊天历史失败: {e}")
            raise
    
    async def get_sessions(self) -> List[Dict[str, Any]]:
        """
        获取所有会话
        """
        try:
            sessions = list(self.sessions_db.values())
            sessions.sort(key=lambda x: x["last_active_time"], reverse=True)
            return sessions
            
        except Exception as e:
            logger.error(f"获取会话列表失败: {e}")
            raise
    
    async def delete_session(self, session_id: str) -> bool:
        """
        删除会话
        """
        try:
            if session_id in self.sessions_db:
                del self.sessions_db[session_id]
            
            if session_id in self.messages_db:
                del self.messages_db[session_id]
            
            logger.info(f"会话 {session_id} 删除成功")
            return True
            
        except Exception as e:
            logger.error(f"删除会话失败: {e}")
            raise
    
    def _generate_session_title(self, first_question: str) -> str:
        """
        根据第一个问题生成会话标题
        """
        # 简单截取前30个字符作为标题
        title = first_question[:30]
        if len(first_question) > 30:
            title += "..."
        return title
    
    async def clear_all_sessions(self) -> bool:
        """
        清空所有会话（用于测试）
        """
        try:
            self.sessions_db.clear()
            self.messages_db.clear()
            logger.info("所有会话已清空")
            return True
            
        except Exception as e:
            logger.error(f"清空会话失败: {e}")
            raise

# 创建全局服务实例
chat_service = ChatService()