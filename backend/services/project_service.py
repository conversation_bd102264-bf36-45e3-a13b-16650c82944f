from typing import List, Optional
from datetime import datetime
from loguru import logger
from sqlalchemy import select, update, delete, func, and_
from sqlalchemy.exc import IntegrityError
from models.schemas import Project, ProjectCreateRequest, ProjectUpdateRequest
from models.database_models import ProjectModel
from services.database_service import database_service
import uuid

class ProjectService:
    def __init__(self):
        # 使用PostgreSQL存储项目数据
        # 默认项目的固定UUID
        self.DEFAULT_PROJECT_ID = "00000000-0000-0000-0000-000000000001"
    
    async def _ensure_default_project(self) -> None:
        """确保默认项目存在"""
        try:
            async with database_service.get_session() as session:
                # 检查默认项目是否存在
                stmt = select(ProjectModel).where(ProjectModel.id == self.DEFAULT_PROJECT_ID)
                result = await session.execute(stmt)
                existing_project = result.scalar_one_or_none()
                
                if not existing_project:
                    # 创建默认项目
                    default_project = ProjectModel(
                        id=self.DEFAULT_PROJECT_ID,
                        name="默认项目",
                        description="系统默认项目，用于存放未分类的文档",
                        document_count=0,
                        is_active=True
                    )
                    session.add(default_project)
                    await session.commit()
                    logger.info("默认项目已创建")
                else:
                    logger.debug("默认项目已存在")
        except Exception as e:
            logger.error(f"确保默认项目存在失败: {e}")
            raise
    
    async def create_project(self, request: ProjectCreateRequest) -> Project:
        """创建新项目"""
        try:
            async with database_service.get_session() as session:
                # 检查项目名称是否已存在
                stmt = select(ProjectModel).where(
                    and_(ProjectModel.name == request.name, ProjectModel.is_active == True)
                )
                result = await session.execute(stmt)
                existing_project = result.scalar_one_or_none()
                
                if existing_project:
                    raise ValueError(f"项目名称 '{request.name}' 已存在")
                
                # 创建新项目
                project_model = ProjectModel(
                    id=str(uuid.uuid4()),
                    name=request.name,
                    description=request.description,
                    document_count=0,
                    is_active=True
                )
                
                session.add(project_model)
                await session.commit()
                await session.refresh(project_model)
                
                logger.info(f"项目创建成功: {project_model.name} (ID: {project_model.id})")
                
                # 转换为Pydantic模型
                return Project(
                    id=project_model.id,
                    name=project_model.name,
                    description=project_model.description,
                    created_at=project_model.created_at,
                    updated_at=project_model.updated_at,
                    document_count=project_model.document_count,
                    is_active=project_model.is_active
                )
                
        except IntegrityError as e:
            logger.error(f"项目名称冲突: {e}")
            raise ValueError(f"项目名称 '{request.name}' 已存在")
        except Exception as e:
            logger.error(f"创建项目失败: {e}")
            raise
    
    async def get_projects(self) -> List[Project]:
        """获取所有活跃项目列表"""
        try:
            await self._ensure_default_project()
            
            async with database_service.get_session() as session:
                stmt = select(ProjectModel).where(
                    ProjectModel.is_active == True
                ).order_by(ProjectModel.created_at.desc())
                
                result = await session.execute(stmt)
                project_models = result.scalars().all()
                
                # 转换为Pydantic模型列表
                projects = [
                    Project(
                        id=project.id,
                        name=project.name,
                        description=project.description,
                        created_at=project.created_at,
                        updated_at=project.updated_at,
                        document_count=project.document_count,
                        is_active=project.is_active
                    )
                    for project in project_models
                ]
                
                return projects
                
        except Exception as e:
            logger.error(f"获取项目列表失败: {e}")
            raise
    
    async def get_project(self, project_id: str) -> Optional[Project]:
        """获取单个项目信息"""
        try:
            async with database_service.get_session() as session:
                stmt = select(ProjectModel).where(
                    and_(ProjectModel.id == project_id, ProjectModel.is_active == True)
                )
                result = await session.execute(stmt)
                project_model = result.scalar_one_or_none()
                
                if project_model:
                    return Project(
                        id=project_model.id,
                        name=project_model.name,
                        description=project_model.description,
                        created_at=project_model.created_at,
                        updated_at=project_model.updated_at,
                        document_count=project_model.document_count,
                        is_active=project_model.is_active
                    )
                return None
                
        except Exception as e:
            logger.error(f"获取项目信息失败: {e}")
            raise
    
    async def update_project(self, project_id: str, request: ProjectUpdateRequest) -> Optional[Project]:
        """更新项目信息"""
        try:
            async with database_service.get_session() as session:
                # 检查项目是否存在
                stmt = select(ProjectModel).where(
                    and_(ProjectModel.id == project_id, ProjectModel.is_active == True)
                )
                result = await session.execute(stmt)
                project_model = result.scalar_one_or_none()
                
                if not project_model:
                    return None
                
                # 检查新名称是否与其他项目冲突
                if request.name:
                    name_check_stmt = select(ProjectModel).where(
                        and_(
                            ProjectModel.name == request.name,
                            ProjectModel.is_active == True,
                            ProjectModel.id != project_id
                        )
                    )
                    name_result = await session.execute(name_check_stmt)
                    existing_project = name_result.scalar_one_or_none()
                    
                    if existing_project:
                        raise ValueError(f"项目名称 '{request.name}' 已存在")
                    
                    project_model.name = request.name
                
                if request.description is not None:
                    project_model.description = request.description
                
                await session.commit()
                await session.refresh(project_model)
                
                logger.info(f"项目更新成功: {project_model.name} (ID: {project_id})")
                
                return Project(
                    id=project_model.id,
                    name=project_model.name,
                    description=project_model.description,
                    created_at=project_model.created_at,
                    updated_at=project_model.updated_at,
                    document_count=project_model.document_count,
                    is_active=project_model.is_active
                )
                
        except Exception as e:
            logger.error(f"更新项目失败: {e}")
            raise
    
    async def delete_project(self, project_id: str) -> bool:
        """删除项目（软删除）"""
        try:
            # 不允许删除默认项目
            if project_id == self.DEFAULT_PROJECT_ID:
                raise ValueError("不能删除默认项目")
            
            async with database_service.get_session() as session:
                stmt = select(ProjectModel).where(
                    and_(ProjectModel.id == project_id, ProjectModel.is_active == True)
                )
                result = await session.execute(stmt)
                project_model = result.scalar_one_or_none()
                
                if not project_model:
                    return False
                
                # 软删除
                project_model.is_active = False
                await session.commit()
                
                logger.info(f"项目删除成功: {project_model.name} (ID: {project_id})")
                return True
                
        except Exception as e:
            logger.error(f"删除项目失败: {e}")
            raise
    
    async def update_document_count(self, project_id: str, delta: int = 1):
        """更新项目的文档数量"""
        try:
            async with database_service.get_session() as session:
                stmt = select(ProjectModel).where(
                    and_(ProjectModel.id == project_id, ProjectModel.is_active == True)
                )
                result = await session.execute(stmt)
                project_model = result.scalar_one_or_none()
                
                if project_model:
                    project_model.document_count = max(0, project_model.document_count + delta)
                    await session.commit()
                    logger.debug(f"项目 {project_model.name} 文档数量更新: {project_model.document_count}")
                    
        except Exception as e:
            logger.error(f"更新项目文档数量失败: {e}")
    
    def get_default_project_id(self) -> str:
        """获取默认项目ID"""
        return self.DEFAULT_PROJECT_ID

# 创建全局项目服务实例
project_service = ProjectService()