import os
import uuid
import aiofiles
from typing import List, Dict, Any, Optional, Tuple
from docx import Document
from loguru import logger
from datetime import datetime
from fastapi import UploadFile
from app.config import settings
from models.schemas import DocumentStatus, Document as DocumentModel
from services.alibaba_client import alibaba_client
from services.chroma_client import chroma_client
import asyncio
import re

class DocumentService:
    def __init__(self):
        self.upload_dir = settings.upload_dir
        self.max_file_size = settings.max_file_size
        self.documents_db = {}  # 简单的内存存储，实际项目中应使用数据库
        self._initialized = False
    
    async def initialize(self):
        """
        初始化文档服务，从ChromaDB恢复已存在的文档信息
        """
        if self._initialized:
            return
        
        try:
            logger.info("🔄 开始初始化文档服务，从ChromaDB恢复文档信息...")
            
            # 从ChromaDB获取所有文档信息
            chroma_documents = await chroma_client.get_all_documents()
            
            if not chroma_documents:
                logger.info("📝 ChromaDB中没有找到已存在的文档")
                self._initialized = True
                return
            
            # 恢复文档信息到内存
            restored_count = 0
            for doc_id, doc_info in chroma_documents.items():
                # 构造文档文件路径
                document_name = doc_info.get('document_name', '未知文档')
                
                # 查找对应的文件
                file_path = None
                if os.path.exists(self.upload_dir):
                    for filename in os.listdir(self.upload_dir):
                        if filename.startswith(doc_id):
                            file_path = os.path.join(self.upload_dir, filename)
                            break
                
                if file_path and os.path.exists(file_path):
                    # 获取文件信息
                    file_size = os.path.getsize(file_path)
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    
                    # 创建文档模型（初始化时分配到默认项目）
                    document = DocumentModel(
                        id=doc_id,
                        filename=os.path.basename(file_path),
                        original_name=document_name,
                        file_path=file_path,
                        status=DocumentStatus.COMPLETED,
                        upload_time=file_mtime,
                        vectorize_time=file_mtime,
                        file_size=file_size,
                        chunk_count=doc_info.get('chunk_count', 0),
                        project_id="default"  # 现有文档分配到默认项目
                    )
                    
                    # 添加到内存数据库
                    self.documents_db[doc_id] = document
                    restored_count += 1
                    
                    logger.info(f"✅ 恢复文档: {document_name} (ID: {doc_id[:8]}...) - {doc_info.get('chunk_count', 0)} 个块")
                else:
                    logger.warning(f"⚠️ 文档文件不存在，跳过: {document_name} (ID: {doc_id[:8]}...)")
            
            logger.info(f"🎉 文档服务初始化完成，成功恢复 {restored_count} 个文档")
            self._initialized = True
            
        except Exception as e:
            logger.error(f"❌ 文档服务初始化失败: {e}")
            # 即使失败也标记为已初始化，避免重复尝试
            self._initialized = True
    
    async def upload_document(self, file_content: bytes, filename: str, project_id: Optional[str] = None) -> DocumentModel:
        """
        上传文档文件
        """
        try:
            # 检查文件大小
            if len(file_content) > self.max_file_size:
                raise Exception(f"文件大小超过限制 ({self.max_file_size} bytes)")
            
            # 检查文件类型
            if not filename.lower().endswith('.docx'):
                raise Exception("只支持DOCX格式文件")
            
            # 生成唯一文档ID和文件名
            document_id = str(uuid.uuid4())
            safe_filename = f"{document_id}_{filename}"
            file_path = os.path.join(self.upload_dir, safe_filename)
            
            # 确保上传目录存在
            os.makedirs(self.upload_dir, exist_ok=True)
            
            # 保存文件
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)
            
            # 验证文件是否成功保存
            if not os.path.exists(file_path):
                raise Exception(f"文件保存失败: {file_path}")
            
            # 验证文件大小
            saved_file_size = os.path.getsize(file_path)
            if saved_file_size != len(file_content):
                os.remove(file_path)  # 删除损坏的文件
                raise Exception(f"文件保存不完整，预期大小: {len(file_content)}, 实际大小: {saved_file_size}")
            
            logger.info(f"文件成功保存到: {file_path}, 大小: {saved_file_size} bytes")
            
            # 创建文档记录
            document = DocumentModel(
                id=document_id,
                filename=safe_filename,
                original_name=filename,
                file_path=file_path,
                status=DocumentStatus.UPLOADED,
                upload_time=datetime.now(),
                file_size=len(file_content),
                project_id=project_id
            )
            
            # 存储到内存数据库
            self.documents_db[document_id] = document
            
            logger.info(f"文档上传成功: {filename} -> {document_id}")
            return document
            
        except Exception as e:
            logger.error(f"文档上传失败: {e}")
            raise
    
    def extract_text_from_docx(self, file_path: str) -> str:
        """
        从DOCX文件提取文本 - 改进版本，提取更完整的内容
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise Exception(f"文档文件不存在: {file_path}")
            
            # 检查文件是否可读
            if not os.access(file_path, os.R_OK):
                raise Exception(f"文档文件无法读取: {file_path}")
            
            logger.info(f"开始提取DOCX文件内容: {file_path}")
            doc = Document(file_path)
            text_content = []
            
            # 统计信息
            paragraph_count = 0
            table_count = 0
            empty_paragraph_count = 0
            
            # 提取段落文本 - 改进版本
            logger.info(f"文档包含 {len(doc.paragraphs)} 个段落")
            for i, paragraph in enumerate(doc.paragraphs):
                para_text = paragraph.text
                
                # 记录所有段落，包括空段落（可能包含格式信息）
                if para_text.strip():
                    # 保留段落的原始格式，包括前后空格
                    text_content.append(para_text.rstrip())
                    paragraph_count += 1
                    
                    # 记录段落样式信息（用于调试）
                    if paragraph.style and paragraph.style.name:
                        logger.debug(f"段落 {i+1}: 样式='{paragraph.style.name}', 长度={len(para_text)}, 内容预览='{para_text[:50]}...'")
                else:
                    # 空段落也保留，可能是格式分隔
                    if para_text == '':
                        text_content.append('')
                    empty_paragraph_count += 1
            
            logger.info(f"提取了 {paragraph_count} 个有内容的段落，{empty_paragraph_count} 个空段落")
            
            # 提取表格文本 - 改进版本
            logger.info(f"文档包含 {len(doc.tables)} 个表格")
            for table_idx, table in enumerate(doc.tables):
                logger.debug(f"处理表格 {table_idx + 1}: {len(table.rows)} 行 x {len(table.columns) if table.rows else 0} 列")
                
                # 添加表格标识
                text_content.append(f"\n[表格 {table_idx + 1}]")
                
                for row_idx, row in enumerate(table.rows):
                    row_text = []
                    for cell_idx, cell in enumerate(row.cells):
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_text.append(cell_text)
                        else:
                            row_text.append("[空]")
                    
                    if row_text:
                        # 使用更清晰的分隔符
                        table_row = " | ".join(row_text)
                        text_content.append(table_row)
                        logger.debug(f"表格 {table_idx + 1} 行 {row_idx + 1}: {table_row[:100]}...")
                
                text_content.append(f"[表格 {table_idx + 1} 结束]\n")
                table_count += 1
            
            logger.info(f"提取了 {table_count} 个表格")
            
            # 合并所有文本内容
            full_text = "\n".join(text_content)
            
            # 清理多余的空行，但保留必要的段落分隔
            lines = full_text.split('\n')
            cleaned_lines = []
            prev_empty = False
            
            for line in lines:
                if line.strip():
                    cleaned_lines.append(line)
                    prev_empty = False
                else:
                    # 只保留一个连续的空行
                    if not prev_empty:
                        cleaned_lines.append('')
                    prev_empty = True
            
            full_text = '\n'.join(cleaned_lines)
            
            # 详细的提取统计
            char_count = len(full_text)
            word_count = len(full_text.split())
            line_count = len([line for line in full_text.split('\n') if line.strip()])
            
            logger.info(f"DOCX文本提取完成:")
            logger.info(f"  - 总字符数: {char_count}")
            logger.info(f"  - 总词数: {word_count}")
            logger.info(f"  - 有效行数: {line_count}")
            logger.info(f"  - 段落数: {paragraph_count}")
            logger.info(f"  - 表格数: {table_count}")
            
            # 显示文本开头和结尾的预览
            preview_length = 200
            if len(full_text) > preview_length * 2:
                logger.info(f"文本开头预览: {full_text[:preview_length]}...")
                logger.info(f"文本结尾预览: ...{full_text[-preview_length:]}")
            else:
                logger.info(f"完整文本预览: {full_text}")
            
            return full_text
            
        except Exception as e:
            logger.error(f"提取DOCX文本失败: {e}")
            raise Exception(f"文档解析失败: {str(e)}")
    
    def split_text_by_sections(self, text: str) -> List[Dict[str, str]]:
        """
        按章节分割文本 - 改进版本，更准确的章节识别和分块
        """
        try:
            logger.info(f"开始按章节分割文本，总长度: {len(text)} 字符")
            
            # 扩展的章节标题正则表达式模式
            section_patterns = [
                # 中文章节模式
                r'^\s*([第][一二三四五六七八九十\d]+[章节部分条][、.]?\s*.+)$',  # 第X章/节/部分/条
                
                # 数字编号模式
                r'^\s*(\d+[、.]\s*.+)$',  # 1. 标题
                r'^\s*(\d+\.\d+\s*.+)$',  # 1.1 标题
                r'^\s*(\d+\.\d+\.\d+\s*.+)$',  # 1.1.1 标题
                r'^\s*(\d+\.\d+\.\d+\.\d+\s*.+)$',  # 1.1.1.1 标题
                
                # 中文序号模式
                r'^\s*([一二三四五六七八九十]+[、.]\s*.+)$',  # 一、标题
                r'^\s*(\([一二三四五六七八九十\d]+\)\s*.+)$',  # (一)标题
                r'^\s*([一二三四五六七八九十]+\s*[、.]\s*.+)$',  # 一 、标题
                
                # 字母和罗马数字模式
                r'^\s*([A-Z][、.]\s*.+)$',  # A. 标题
                r'^\s*([IVX]+[、.]\s*.+)$',  # I. II. III. 等罗马数字
                
                # 特殊格式
                r'^\s*(【.*】.*)$',  # 【标题】
                r'^\s*(\*\*.*\*\*)$',  # **标题**
                r'^\s*(#+\s+.*)$',  # # 标题 (Markdown格式)
                
                # 关键词章节
                r'^\s*(项目背景|产品需求|核心功能|用户群体|技术方案|实施计划|概述|简介|背景|目标|方法|结论|总结|附录|参考|说明).*$',
                
                # 表格标识
                r'^\s*(\[表格\s+\d+\])$',  # [表格 1]
            ]
            
            lines = text.split('\n')
            sections = []
            current_section = {'title': '文档开头', 'content': ''}
            
            logger.info(f"文本共 {len(lines)} 行")
            
            section_count = 0
            for i, line in enumerate(lines):
                original_line = line
                line = line.strip()
                
                # 跳过完全空行
                if not line:
                    current_section['content'] += '\n'
                    continue
                
                # 检查是否是章节标题
                is_section_title = False
                matched_pattern = None
                
                for pattern in section_patterns:
                    if re.match(pattern, line, re.MULTILINE):
                        is_section_title = True
                        matched_pattern = pattern
                        break
                
                # 额外的启发式规则：短行且包含关键词可能是标题
                if not is_section_title and len(line) < 100:
                    title_keywords = ['概述', '简介', '背景', '目标', '方法', '结论', '总结', '附录', '参考', '说明']
                    if any(keyword in line for keyword in title_keywords):
                        # 进一步检查：如果下一行不为空且不是很短，可能是标题
                        if i + 1 < len(lines) and len(lines[i + 1].strip()) > 20:
                            is_section_title = True
                            matched_pattern = "启发式规则"
                
                if is_section_title and current_section['content'].strip():
                    # 保存当前章节
                    current_section['content'] = current_section['content'].strip()
                    
                    # 降低最小内容长度要求，避免丢失短章节
                    if len(current_section['content']) > 20:  # 从50降低到20
                        sections.append(current_section)
                        section_count += 1
                        logger.debug(f"保存章节 {section_count}: '{current_section['title'][:50]}...', 内容长度: {len(current_section['content'])}")
                    else:
                        logger.debug(f"跳过过短章节: '{current_section['title'][:50]}...', 内容长度: {len(current_section['content'])}")
                    
                    # 开始新章节
                    current_section = {
                        'title': line,
                        'content': ''
                    }
                    logger.debug(f"识别新章节标题 (第{i+1}行): '{line}', 匹配模式: {matched_pattern}")
                else:
                    # 保留原始格式，包括缩进
                    current_section['content'] += original_line + '\n'
            
            # 添加最后一个章节
            if current_section['content'].strip():
                current_section['content'] = current_section['content'].strip()
                if len(current_section['content']) > 20:
                    sections.append(current_section)
                    section_count += 1
                    logger.debug(f"保存最后章节 {section_count}: '{current_section['title'][:50]}...', 内容长度: {len(current_section['content'])}")
            
            logger.info(f"章节识别完成，共识别 {len(sections)} 个章节")
            
            # 如果没有识别到足够的章节，使用混合策略
            if len(sections) <= 1:
                logger.info("识别到的章节数量过少，使用固定长度分割策略")
                return self._split_by_length(text)
            
            # 检查章节大小，如果某些章节过大，进一步分割
            final_sections = []
            max_section_length = 3000  # 单个章节最大长度
            
            for section in sections:
                if len(section['content']) > max_section_length:
                    logger.info(f"章节 '{section['title'][:30]}...' 过长 ({len(section['content'])} 字符)，进行二次分割")
                    # 对过长的章节按段落进一步分割
                    sub_sections = self._split_long_section(section, max_section_length)
                    final_sections.extend(sub_sections)
                else:
                    final_sections.append(section)
            
            logger.info(f"最终分割完成，共 {len(final_sections)} 个文本块")
            
            # 输出每个章节的统计信息
            for i, section in enumerate(final_sections):
                logger.debug(f"文本块 {i+1}: 标题='{section['title'][:30]}...', 长度={len(section['content'])} 字符")
            
            return final_sections
            
        except Exception as e:
            logger.error(f"按章节分割失败: {e}")
            return self._split_by_length(text)
    
    def _split_by_length(self, text: str, chunk_size: int = 800) -> List[Dict[str, str]]:
        """
        按固定长度分割文本（备用方案）- 改进版本，保持段落完整性
        """
        logger.info(f"使用固定长度分割，最大长度: {chunk_size} 字符")
        
        chunks = []
        paragraphs = text.split('\n\n')  # 按双换行符分割段落
        current_chunk = ""
        chunk_index = 1
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # 如果单个段落就超过最大长度，需要强制分割
            if len(paragraph) > chunk_size:
                # 先保存当前块
                if current_chunk:
                    chunks.append({
                        'title': f'第{chunk_index}段',
                        'content': current_chunk.strip()
                    })
                    chunk_index += 1
                    current_chunk = ""
                
                # 按句子分割长段落
                sentences = re.split(r'[。！？.!?]', paragraph)
                temp_chunk = ""
                
                for sentence in sentences:
                    sentence = sentence.strip()
                    if not sentence:
                        continue
                    
                    # 恢复句号
                    if not sentence.endswith(('。', '！', '？', '.', '!', '?')):
                        sentence += '。'
                    
                    if len(temp_chunk) + len(sentence) <= chunk_size:
                        temp_chunk += sentence
                    else:
                        if temp_chunk:
                            chunks.append({
                                'title': f'第{chunk_index}段',
                                'content': temp_chunk.strip()
                            })
                            chunk_index += 1
                        temp_chunk = sentence
                
                if temp_chunk:
                    current_chunk = temp_chunk
            
            # 检查是否可以添加到当前块
            elif len(current_chunk) + len(paragraph) + 2 <= chunk_size:  # +2 for \n\n
                current_chunk += ("\n\n" if current_chunk else "") + paragraph
            else:
                # 保存当前块，开始新块
                if current_chunk:
                    chunks.append({
                        'title': f'第{chunk_index}段',
                        'content': current_chunk.strip()
                    })
                    chunk_index += 1
                current_chunk = paragraph
        
        # 保存最后一个块
        if current_chunk:
            chunks.append({
                'title': f'第{chunk_index}段',
                'content': current_chunk.strip()
            })
        
        logger.info(f"按长度分割完成，共 {len(chunks)} 个块")
        return chunks
    
    def _split_long_section(self, section: Dict[str, str], max_length: int) -> List[Dict[str, str]]:
        """
        分割过长的章节
        """
        content = section['content']
        title = section['title']
        
        # 首先尝试按段落分割
        paragraphs = content.split('\n\n')
        sub_sections = []
        current_content = ""
        sub_index = 1
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            if len(current_content) + len(paragraph) + 2 <= max_length:
                current_content += ("\n\n" if current_content else "") + paragraph
            else:
                if current_content:
                    sub_sections.append({
                        'title': f"{title} (第{sub_index}部分)",
                        'content': current_content.strip()
                    })
                    sub_index += 1
                
                # 如果单个段落过长，使用固定长度分割
                if len(paragraph) > max_length:
                    long_chunks = self._split_by_length(paragraph, max_length)
                    for i, chunk in enumerate(long_chunks):
                        chunk['title'] = f"{title} (第{sub_index}部分)"
                        sub_sections.append(chunk)
                        sub_index += 1
                    current_content = ""
                else:
                    current_content = paragraph
        
        # 保存最后一部分
        if current_content:
            sub_sections.append({
                'title': f"{title} (第{sub_index}部分)",
                'content': current_content.strip()
            })
        
        return sub_sections
    
    def split_text_into_chunks(self, text: str) -> List[str]:
        """
        将文本按章节分割成块，返回文本列表（保持兼容性）
        """
        try:
            # 使用章节分割
            sections = self.split_text_by_sections(text)
            
            # 转换为文本列表格式
            chunks = []
            for section in sections:
                # 组合标题和内容
                if section['title'] != '文档开头':
                    chunk_text = f"{section['title']}\n\n{section['content']}"
                else:
                    chunk_text = section['content']
                
                chunks.append(chunk_text.strip())
            
            # 过滤空块
            chunks = [chunk for chunk in chunks if chunk.strip()]
            
            logger.info(f"文本分割完成，共 {len(chunks)} 个块")
            return chunks
            
        except Exception as e:
            logger.error(f"文本分割失败: {e}")
            raise
    
    async def vectorize_document(self, document_id: str) -> Dict[str, Any]:
        """
        对文档进行向量化处理 - 改进版本，增强错误处理和日志记录
        """
        try:
            logger.info(f"开始向量化文档: {document_id}")
            
            # 获取文档记录
            if document_id not in self.documents_db:
                raise Exception(f"文档 {document_id} 不存在")
            
            document = self.documents_db[document_id]
            
            logger.info(f"文档信息: 名称='{document.original_name}', 路径='{document.file_path}', 当前状态='{document.status}'")
            
            # 检查文档状态
            if document.status == DocumentStatus.COMPLETED:
                logger.info(f"文档 {document_id} 已经向量化，跳过处理")
                return {
                    "success": True,
                    "status": "completed",
                    "task_id": document_id,
                    "chunks_count": document.chunk_count or 0
                }
            
            # 检查文件是否存在
            if not os.path.exists(document.file_path):
                raise Exception(f"文档文件不存在: {document.file_path}")
            
            # 更新状态为处理中
            document.status = DocumentStatus.PROCESSING
            logger.info(f"文档状态已更新为 'PROCESSING'")
            
            # 提取文本
            logger.info(f"开始提取文档文本，文件类型: {os.path.splitext(document.file_path)[1]}")
            text_content = self.extract_text_from_docx(document.file_path)
            
            if not text_content.strip():
                raise Exception(f"文档内容为空，提取的文本长度: {len(text_content)}")
            
            logger.info(f"文本提取完成，总长度: {len(text_content)} 字符")
            
            # 分割文本（获取章节信息）
            logger.info("开始分割文本为章节")
            sections = self.split_text_by_sections(text_content)
            
            if not sections:
                logger.error("文本分割失败，未生成任何章节")
                raise Exception("文档分割后无有效内容")
            
            logger.info(f"文本分割完成，共生成 {len(sections)} 个章节")
            
            # 显示每个章节的详细信息
            for i, section in enumerate(sections):
                logger.info(f"章节 {i+1}: 标题='{section['title'][:50]}...', 内容长度={len(section['content'])} 字符")
            
            # 转换为文本列表用于向量化
            text_chunks = []
            section_info = []
            
            for section in sections:
                # 组合标题和内容
                if section['title'] != '文档开头':
                    chunk_text = f"{section['title']}\n\n{section['content']}"
                else:
                    chunk_text = section['content']
                
                text_chunks.append(chunk_text.strip())
                section_info.append(section)
            
            # 过滤空块
            filtered_chunks = []
            filtered_sections = []
            failed_chunks = 0
            
            for i, chunk in enumerate(text_chunks):
                if chunk.strip() and len(chunk.strip()) >= 5:
                    filtered_chunks.append(chunk)
                    filtered_sections.append(section_info[i])
                else:
                    logger.warning(f"跳过空章节或过短章节 {i+1}: '{section_info[i]['title']}'")
                    failed_chunks += 1
            
            text_chunks = filtered_chunks
            section_info = filtered_sections
            
            if not text_chunks:
                raise Exception(f"文档分割后无有效内容，总共尝试了 {len(sections)} 个章节")
            
            logger.info(f"章节过滤完成: 有效 {len(text_chunks)} 个，跳过 {failed_chunks} 个")
            
            # 获取向量化结果
            logger.info(f"🔄 开始向量化 {len(text_chunks)} 个文本块")
            logger.info(f"📊 文本块统计: 平均长度={sum(len(chunk) for chunk in text_chunks)//len(text_chunks)} 字符")
            
            embeddings = await alibaba_client.get_embeddings(text_chunks)
            
            if len(embeddings) != len(text_chunks):
                raise Exception(f"向量化结果数量与文本块数量不匹配: {len(embeddings)} vs {len(text_chunks)}")
            
            logger.info(f"✅ 向量化完成，生成 {len(embeddings)} 个向量，每个向量维度: {len(embeddings[0]) if embeddings else 0}")
            
            # 准备元数据（包含章节信息和项目信息）
            metadatas = []
            for i, (chunk, section) in enumerate(zip(text_chunks, section_info)):
                metadatas.append({
                    "document_id": document_id,
                    "document_name": document.original_name,
                    "project_id": document.project_id or "default",
                    "chunk_index": i,
                    "chunk_length": len(chunk),
                    "section_title": section['title'],
                    "created_time": datetime.now().isoformat()
                })
            
            logger.info(f"📋 元数据准备完成，包含文档名称、块索引、长度等信息")
            
            # 存储到ChromaDB
            logger.info(f"💾 开始存储到ChromaDB向量数据库...")
            vector_ids = await chroma_client.add_documents(
                document_id=document_id,
                texts=text_chunks,
                embeddings=embeddings,
                metadatas=metadatas
            )
            
            logger.info(f"✅ 成功存储到ChromaDB，生成向量ID数量: {len(vector_ids)}")
            logger.info(f"📝 向量ID示例: {vector_ids[:3] if len(vector_ids) >= 3 else vector_ids}")
            
            # 更新文档状态
            document.status = DocumentStatus.COMPLETED
            document.vectorize_time = datetime.now()
            document.chunk_count = len(text_chunks)
            
            logger.info(f"文档 {document_id} 向量化完成，共 {len(text_chunks)} 个块")
            
            return {
                "success": True,
                "status": "completed",
                "task_id": document_id,
                "chunks_count": len(text_chunks)
            }
            
        except Exception as e:
            logger.error(f"文档向量化失败: {e}")
            logger.error(f"错误详情: {str(e)}")
            
            # 更新状态为失败
            if document_id in self.documents_db:
                self.documents_db[document_id].status = DocumentStatus.FAILED
                logger.info(f"文档状态已更新为 'FAILED'")
            
            raise Exception(f"向量化失败: {str(e)}")
    
    async def get_document(self, document_id: str) -> Optional[DocumentModel]:
        """
        获取单个文档信息
        """
        try:
            return self.documents_db.get(document_id)
        except Exception as e:
            logger.error(f"获取文档信息失败: {e}")
            raise
    
    async def get_documents(self, page: int = 1, page_size: int = 10, project_id: Optional[str] = None) -> Dict[str, Any]:
        """
        获取文档列表
        """
        try:
            # 确保服务已初始化
            await self.initialize()
            
            documents = list(self.documents_db.values())
            
            # 如果指定了项目ID，则只返回该项目的文档
            if project_id:
                documents = [doc for doc in documents if doc.project_id == project_id]
            
            documents.sort(key=lambda x: x.upload_time, reverse=True)
            
            total = len(documents)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            
            page_documents = documents[start_idx:end_idx]
            
            return {
                "documents": page_documents,
                "total": total,
                "page": page,
                "page_size": page_size
            }
            
        except Exception as e:
            logger.error(f"获取文档列表失败: {e}")
            raise
    
    async def delete_document(self, document_id: str) -> bool:
        """
        删除文档
        """
        try:
            if document_id not in self.documents_db:
                raise Exception(f"文档 {document_id} 不存在")
            
            document = self.documents_db[document_id]
            
            # 删除向量数据
            try:
                await chroma_client.delete_document(document_id)
                logger.info(f"向量数据删除成功: {document_id}")
            except Exception as e:
                logger.warning(f"删除向量数据失败: {e}")
                # 继续执行，不因向量删除失败而中断整个删除流程
            
            # 删除文件
            if os.path.exists(document.file_path):
                try:
                    # 检查文件权限
                    if os.access(document.file_path, os.W_OK):
                        os.remove(document.file_path)
                        logger.info(f"文件删除成功: {document.file_path}")
                    else:
                        logger.warning(f"文件无写权限，无法删除: {document.file_path}")
                except OSError as e:
                    logger.warning(f"文件删除失败: {e}")
                    # 继续执行，不因文件删除失败而中断
            else:
                logger.info(f"文件不存在，跳过删除: {document.file_path}")
            
            # 删除记录
            del self.documents_db[document_id]
            
            logger.info(f"文档 {document_id} 删除成功")
            return True
            
        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            raise

# 创建全局服务实例
document_service = DocumentService()