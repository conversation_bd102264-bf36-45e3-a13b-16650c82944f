import numpy as np
from openai import OpenAI
from docx import Document
from loguru import logger
import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from models.schemas import DocumentSummary, DuplicateDetectionResult
from app.config import settings
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import jieba

class DuplicateDetectionService:
    def __init__(self):
        # 阿里云配置
        self.alibaba_api_key = "sk-facd76d0f7fb4ab18d31af8b318f3de6"
        self.alibaba_client = OpenAI(
            api_key=self.alibaba_api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        
        # 相似度阈值配置
        self.warning_threshold = 0.30  # 30%以上显示警告，需要人工判断
        self.block_threshold = 0.50    # 50%以上禁止导入，视为重复项目
        self.similarity_threshold = 0.25  # 保留原有阈值用于兼容性
        
        # 文档存储（用于相似度比较）
        self.document_chunks = {}  # {document_id: {"chunks": [], "embeddings": [], "metadata": {}}}
        
        # TF-IDF向量化器
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words=None,
            ngram_range=(1, 2)
        )
    
    def extract_text_from_docx(self, file_path: str) -> str:
        """
        从DOCX文件提取文本
        """
        try:
            doc = Document(file_path)
            text_content = []

            # 提取段落文本
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text.strip())

            # 提取表格文本
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content.append(" | ".join(row_text))

            full_text = "\n".join(text_content)
            logger.info(f"从DOCX文件提取文本，长度: {len(full_text)} 字符")
            return full_text
        except Exception as e:
            logger.error(f"提取DOCX文本失败: {e}")
            raise Exception(f"文档解析失败: {str(e)}")
    
    def split_text_by_sections(self, text: str) -> List[Dict[str, str]]:
        """
        按章节分割文本
        """
        try:
            # 章节标题模式
            section_patterns = [
                r'^\s*([一二三四五六七八九十]+[、.]\s*.+)$',  # 中文数字章节
                r'^\s*(\d+[、.]\s*.+)$',  # 阿拉伯数字章节
                r'^\s*([第][一二三四五六七八九十]+[章节部分][、.]?\s*.+)$',  # 第X章
                r'^\s*(Chapter\s+\d+.*)$',  # 英文章节
                r'^\s*([A-Z][、.]\s*.+)$',  # 字母章节
                r'^\s*(项目背景|产品需求|核心功能|用户群体|技术方案|实施计划).*$',  # 关键词章节
            ]
            
            lines = text.split('\n')
            sections = []
            current_section = {'title': '文档开始', 'content': ''}
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                # 检查是否是章节标题
                is_section_title = False
                for pattern in section_patterns:
                    if re.match(pattern, line, re.IGNORECASE):
                        # 保存当前章节
                        if current_section['content'].strip():
                            sections.append(current_section)
                        
                        # 开始新章节
                        current_section = {
                            'title': line,
                            'content': ''
                        }
                        is_section_title = True
                        break
                
                if not is_section_title:
                    current_section['content'] += line + '\n'
            
            # 添加最后一个章节
            if current_section['content'].strip():
                sections.append(current_section)
            
            # 如果没有找到章节，按固定长度分块
            if len(sections) <= 1:
                logger.info("未找到明显的章节结构，使用固定长度分块")
                return self._split_by_length(text)
            
            logger.info(f"按章节分割完成，共 {len(sections)} 个章节")
            return sections
            
        except Exception as e:
            logger.error(f"章节分割失败: {e}")
            return self._split_by_length(text)
    
    def _split_by_length(self, text: str, chunk_size: int = 800) -> List[Dict[str, str]]:
        """
        按固定长度分割文本（备用方案）
        """
        chunks = []
        start = 0
        chunk_index = 1
        
        while start < len(text):
            end = start + chunk_size
            if end >= len(text):
                chunk_content = text[start:]
            else:
                # 尝试在句号处分割
                chunk_content = text[start:end]
                last_period = chunk_content.rfind('。')
                if last_period > chunk_size // 2:
                    end = start + last_period + 1
                    chunk_content = text[start:end]
            
            chunks.append({
                'title': f'文档片段 {chunk_index}',
                'content': chunk_content.strip()
            })
            
            start = end
            chunk_index += 1
        
        return chunks
    
    async def extract_document_summary(self, text_content: str) -> DocumentSummary:
        """
        使用阿里云LLM提取文档摘要信息
        """
        try:
            system_prompt = """
            请你仔细阅读以下文档内容，然后从中提取并总结出四个关键部分的信息。
            请直接按照以下格式输出，每个部分用"###"开头：

            ### 项目背景
            [项目背景内容]

            ### 产品需求
            [产品需求内容]

            ### 产品核心功能
            [产品核心功能内容，要详细描述所有功能]

            ### 用户群体
            [用户群体内容]

            要求：
            1. 项目背景：说明该项目提出的缘由、相关的环境或前提情况
            2. 产品需求：指出项目要解决的问题、满足的要求或达成的目标
            3. 产品核心功能：详细描述产品的所有主要功能特性
            4. 用户群体：明确产品的目标使用对象或受众范围
            """
            
            prompt = f"""
            请分析以下文档内容并提取摘要信息：
            
            {text_content}
            """
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ]
            
            response = self.alibaba_client.chat.completions.create(
                model="qwen-plus-latest",
                messages=messages,
                temperature=0.7,
                max_tokens=2000,
                top_p=0.8
            )

            response_text = response.choices[0].message.content
            logger.info(f"成功获取LLM回答，长度: {len(response_text)}")

            # 提取四个关键部分
            summary_parts = self._parse_llm_response(response_text)
            
            return DocumentSummary(
                project_background=summary_parts[0],
                product_requirements=summary_parts[1],
                core_functions=summary_parts[2],
                target_users=summary_parts[3]
            )
            
        except Exception as e:
            logger.error(f"提取文档摘要失败: {e}")
            raise Exception(f"文档摘要提取失败: {str(e)}")
    
    def _parse_llm_response(self, response_text: str) -> List[str]:
        """
        解析LLM响应，提取四个关键部分
        """
        logger.info(f"开始解析LLM响应，长度: {len(response_text)}")
        logger.info(f"LLM响应内容: {response_text[:500]}...")
        
        # 简单的分割方式，按照"###"标记分割
        sections = response_text.split('###')
        
        response_result = []
        section_names = ["项目背景", "产品需求", "产品核心功能", "用户群体"]
        
        # 查找每个部分的内容
        for section_name in section_names:
            content = ""
            for section in sections:
                if section_name in section:
                    # 提取标题后的内容
                    lines = section.split('\n')
                    content_lines = []
                    found_title = False
                    
                    for line in lines:
                        line = line.strip()
                        if section_name in line:
                            found_title = True
                            continue
                        if found_title and line:
                            content_lines.append(line)
                    
                    content = ' '.join(content_lines).strip()
                    if content:
                        logger.info(f"成功提取{section_name}: {content[:50]}...")
                        break
            
            if not content:
                logger.warning(f"未能提取{section_name}，使用默认内容")
                content = f"未提取到{section_name}内容"
            
            response_result.append(content)
        
        logger.info(f"LLM响应解析完成，提取到 {len(response_result)} 个部分")
        return response_result
    
    async def get_text_embedding(self, text: str) -> List[float]:
        """
        获取文本的向量表示
        """
        try:
            response = self.alibaba_client.embeddings.create(
                model="text-embedding-v1",
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"获取文本向量失败: {e}")
            return []
    
    def calculate_cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """
        计算余弦相似度
        """
        try:
            if not vec1 or not vec2:
                return 0.0
            
            vec1 = np.array(vec1)
            vec2 = np.array(vec2)
            
            # 计算余弦相似度
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
        except Exception as e:
            logger.error(f"计算余弦相似度失败: {e}")
            return 0.0
    
    def calculate_keyword_similarity(self, text1: str, text2: str) -> float:
        """
        计算关键词相似度（基于TF-IDF）
        """
        try:
            # 使用jieba分词
            words1 = ' '.join(jieba.cut(text1))
            words2 = ' '.join(jieba.cut(text2))
            
            # 计算TF-IDF向量
            tfidf_matrix = self.tfidf_vectorizer.fit_transform([words1, words2])
            
            # 计算余弦相似度
            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
            return float(similarity)
        except Exception as e:
            logger.error(f"计算关键词相似度失败: {e}")
            return 0.0
    
    def calculate_structural_similarity(self, sections1: List[Dict], sections2: List[Dict]) -> float:
        """
        计算结构相似度（基于章节标题）
        """
        try:
            titles1 = [section['title'].lower() for section in sections1]
            titles2 = [section['title'].lower() for section in sections2]
            
            # 计算标题匹配度
            matches = 0
            total = max(len(titles1), len(titles2))
            
            for title1 in titles1:
                for title2 in titles2:
                    # 简单的字符串相似度检查
                    if title1 in title2 or title2 in title1:
                        matches += 1
                        break
            
            return matches / total if total > 0 else 0.0
        except Exception as e:
            logger.error(f"计算结构相似度失败: {e}")
            return 0.0
    
    def _validate_document_exists(self, document_id: str) -> bool:
        """
        验证文档是否在document_service中存在
        """
        try:
            from services.document_service import document_service
            return document_id in document_service.documents_db
        except Exception as e:
            logger.error(f"验证文档存在性失败: {e}")
            return False
    
    def _filter_valid_documents(self) -> Dict[str, Any]:
        """
        过滤出在document_service中存在的有效文档
        """
        try:
            from services.document_service import document_service
            valid_documents = {}
            invalid_doc_ids = []
            
            for doc_id, doc_data in self.document_chunks.items():
                if doc_id in document_service.documents_db:
                    valid_documents[doc_id] = doc_data
                else:
                    invalid_doc_ids.append(doc_id)
                    doc_name = doc_data.get('metadata', {}).get('document_name', f'文档_{doc_id}')
                    logger.warning(f"文档 {doc_name} (ID: {doc_id}) 不在文档管理系统中，已过滤")
            
            # 自动清理无效文档
            for doc_id in invalid_doc_ids:
                self.remove_document_from_comparison(doc_id)
            
            logger.info(f"文档过滤完成，有效文档: {len(valid_documents)}，已清理无效文档: {len(invalid_doc_ids)}")
            return valid_documents
            
        except Exception as e:
            logger.error(f"过滤有效文档失败: {e}")
            return self.document_chunks
    
    async def local_similarity_search(self, query_content: str, query_sections: List[Dict]) -> Tuple[float, float, float, str, str]:
        """
        本地相似度搜索（替代RAGFlow）
        只使用文档管理系统中存在的文档进行对比
        """
        try:
            if not self.document_chunks:
                logger.info("没有已存储的文档用于比较")
                return 0.0, 0.0, 0.0, "无文档", "无内容"
            
            # 过滤出有效文档（只使用document_service中存在的文档）
            valid_documents = self._filter_valid_documents()
            
            if not valid_documents:
                logger.info("没有有效的文档用于比较")
                return 0.0, 0.0, 0.0, "无有效文档", "无内容"
            
            max_semantic_similarity = 0.0
            max_keyword_similarity = 0.0
            max_structural_similarity = 0.0
            best_match_doc = "无匹配文档"
            best_match_content = "无匹配内容"
            
            # 获取查询内容的向量
            query_embedding = await self.get_text_embedding(query_content)
            
            # 与所有有效文档进行比较
            for doc_id, doc_data in valid_documents.items():
                doc_sections = doc_data.get('sections', [])
                doc_embeddings = doc_data.get('embeddings', [])
                doc_metadata = doc_data.get('metadata', {})
                
                # 计算语义相似度（向量相似度）
                semantic_similarities = []
                for embedding in doc_embeddings:
                    if embedding:  # 确保向量不为空
                        sim = self.calculate_cosine_similarity(query_embedding, embedding)
                        semantic_similarities.append(sim)
                
                avg_semantic_sim = sum(semantic_similarities) / len(semantic_similarities) if semantic_similarities else 0.0
                
                # 计算关键词相似度
                doc_content = ' '.join([section.get('content', '') for section in doc_sections])
                keyword_sim = self.calculate_keyword_similarity(query_content, doc_content)
                
                # 计算结构相似度
                structural_sim = self.calculate_structural_similarity(query_sections, doc_sections)
                
                # 计算综合相似度（语义40%，关键词40%，结构20%）
                combined_sim = (avg_semantic_sim * 0.4 + keyword_sim * 0.4 + structural_sim * 0.2)
                
                logger.info(f"文档 {doc_id} 相似度详情: 语义={avg_semantic_sim:.3f}, 关键词={keyword_sim:.3f}, 结构={structural_sim:.3f}, 综合={combined_sim:.3f}")
                
                # 更新最佳匹配（基于综合相似度）
                if combined_sim > (max_semantic_similarity * 0.4 + max_keyword_similarity * 0.4 + max_structural_similarity * 0.2):
                    max_semantic_similarity = avg_semantic_sim
                    max_keyword_similarity = keyword_sim
                    max_structural_similarity = structural_sim
                    best_match_doc = doc_metadata.get('document_name', f'文档_{doc_id}')
                    best_match_content = doc_content  # 返回完整内容，不截断
            
            logger.info(f"本地相似度搜索完成，最高语义相似度: {max_semantic_similarity:.3f}")
            return max_semantic_similarity, max_keyword_similarity, max_structural_similarity, best_match_doc, best_match_content
            
        except Exception as e:
            logger.error(f"本地相似度搜索失败: {e}")
            return 0.0, 0.0, 0.0, "搜索失败", str(e)
    
    async def store_document_for_comparison(self, document_id: str, file_path: str, document_name: str):
        """
        存储文档用于后续相似度比较
        只有在document_service中存在的文档才能被存储
        """
        try:
            # 验证文档是否在document_service中存在
            if not self._validate_document_exists(document_id):
                logger.warning(f"文档 {document_name} (ID: {document_id}) 不在文档管理系统中，拒绝存储")
                raise Exception(f"文档 {document_name} 不在文档管理系统中，无法存储用于相似度比较")
            
            # 提取文档文本
            text_content = self.extract_text_from_docx(file_path)
            
            # 按章节分割
            sections = self.split_text_by_sections(text_content)
            
            # 获取每个章节的向量表示
            embeddings = []
            for section in sections:
                content = section['content']
                if content.strip():
                    embedding = await self.get_text_embedding(content)
                    embeddings.append(embedding)
                else:
                    embeddings.append([])
            
            # 存储文档数据
            self.document_chunks[document_id] = {
                'sections': sections,
                'embeddings': embeddings,
                'metadata': {
                    'document_name': document_name,
                    'file_path': file_path,
                    'created_time': datetime.now().isoformat()
                }
            }
            
            logger.info(f"文档 {document_id} 已验证并存储用于相似度比较，共 {len(sections)} 个章节")
            
        except Exception as e:
            logger.error(f"存储文档失败: {e}")
            raise Exception(f"存储文档失败: {str(e)}")
    
    def remove_document_from_comparison(self, document_id: str):
        """
        从相似度比较中移除指定文档
        """
        try:
            if document_id in self.document_chunks:
                del self.document_chunks[document_id]
                logger.info(f"文档 {document_id} 已从相似度比较数据中移除")
                return True
            else:
                logger.warning(f"文档 {document_id} 不在相似度比较数据中")
                return False
        except Exception as e:
            logger.error(f"移除文档 {document_id} 失败: {e}")
            return False
    
    def get_stored_documents_info(self) -> Dict[str, Any]:
        """
        获取当前存储的所有文档信息（用于调试）
        """
        try:
            documents_info = {}
            for doc_id, doc_data in self.document_chunks.items():
                metadata = doc_data.get('metadata', {})
                documents_info[doc_id] = {
                    'document_name': metadata.get('document_name', '未知文档'),
                    'file_path': metadata.get('file_path', '未知路径'),
                    'created_time': metadata.get('created_time', '未知时间'),
                    'sections_count': len(doc_data.get('sections', [])),
                    'embeddings_count': len(doc_data.get('embeddings', []))
                }
            
            logger.info(f"当前存储了 {len(documents_info)} 个文档用于相似度比较")
            return {
                'total_count': len(documents_info),
                'documents': documents_info
            }
        except Exception as e:
            logger.error(f"获取存储文档信息失败: {e}")
            return {'total_count': 0, 'documents': {}, 'error': str(e)}
    
    def sync_with_document_service(self, existing_document_ids: List[str]) -> Dict[str, Any]:
        """
        与document_service同步，清理不存在的文档数据
        """
        try:
            removed_documents = []
            current_doc_ids = list(self.document_chunks.keys())
            
            for doc_id in current_doc_ids:
                if doc_id not in existing_document_ids:
                    doc_info = self.document_chunks[doc_id].get('metadata', {})
                    doc_name = doc_info.get('document_name', f'文档_{doc_id}')
                    
                    # 移除不存在的文档
                    del self.document_chunks[doc_id]
                    removed_documents.append({
                        'document_id': doc_id,
                        'document_name': doc_name
                    })
                    logger.info(f"已清理不存在的文档: {doc_name} (ID: {doc_id})")
            
            result = {
                'removed_count': len(removed_documents),
                'removed_documents': removed_documents,
                'remaining_count': len(self.document_chunks)
            }
            
            logger.info(f"同步完成，清理了 {len(removed_documents)} 个不存在的文档")
            return result
            
        except Exception as e:
            logger.error(f"同步文档数据失败: {e}")
            return {'error': str(e), 'removed_count': 0, 'removed_documents': []}
    
    def clear_all_stored_documents(self) -> Dict[str, Any]:
        """
        清空所有存储的文档数据（谨慎使用）
        """
        try:
            cleared_count = len(self.document_chunks)
            cleared_documents = []
            
            for doc_id, doc_data in self.document_chunks.items():
                metadata = doc_data.get('metadata', {})
                cleared_documents.append({
                    'document_id': doc_id,
                    'document_name': metadata.get('document_name', f'文档_{doc_id}')
                })
            
            self.document_chunks.clear()
            
            logger.warning(f"已清空所有存储的文档数据，共清理 {cleared_count} 个文档")
            return {
                'cleared_count': cleared_count,
                'cleared_documents': cleared_documents
            }
            
        except Exception as e:
            logger.error(f"清空文档数据失败: {e}")
            return {'error': str(e), 'cleared_count': 0}
    
    async def load_existing_documents_to_chunks(self):
        """
        加载文档管理系统中已存在的文档到document_chunks中
        """
        try:
            from services.document_service import document_service
            await document_service.initialize()
            
            loaded_count = 0
            for doc_id, document in document_service.documents_db.items():
                # 加载已完成向量化的文档（包括未进行过重复检测的文档）
                if (doc_id not in self.document_chunks and 
                    document.status.value == "completed"):
                    
                    # 如果文档已进行过重复检测且是重复文档，则跳过
                    if (document.duplicate_detection and 
                        document.duplicate_detection.is_duplicate):
                        logger.info(f"跳过重复文档 {document.original_name} (ID: {doc_id})")
                        continue
                    
                    try:
                        await self.store_document_for_comparison(
                            doc_id, 
                            document.file_path, 
                            document.original_name
                        )
                        loaded_count += 1
                        logger.info(f"已加载文档 {document.original_name} (ID: {doc_id}) 到相似度比较数据中")
                    except Exception as e:
                        logger.warning(f"加载文档 {document.original_name} 失败: {e}")
            
            logger.info(f"完成加载，共加载了 {loaded_count} 个文档到相似度比较数据中")
            return loaded_count
        except Exception as e:
            logger.error(f"加载已存在文档失败: {e}")
            return 0
    
    async def ai_duplicate_analysis(self, document_id: str = None, file_path: str = None, document_name: str = None, current_content: str = None, similar_content: str = None, current_name: str = None, similar_name: str = None) -> Dict[str, Any]:
        """
        使用大模型进行重复建设项目的智能判断
        支持两种调用方式：
        1. 传入document_id和file_path，自动提取内容进行分析
        2. 传入current_content等参数，直接使用提供的内容进行分析
        """
        try:
            # 如果提供了文档ID和文件路径，自动提取内容
            if document_id and file_path:
                # 提取文档文本
                text_content = self.extract_text_from_docx(file_path)
                
                # 提取文档摘要
                summary = await self.extract_document_summary(text_content)
                
                # 构建当前文档内容
                current_content = f"项目背景：{summary.project_background}\n\n产品需求：{summary.product_requirements}\n\n核心功能：{summary.core_functions}\n\n用户群体：{summary.target_users}"
                current_name = document_name or "当前文档"
                
                # 查找相似文档内容
                current_sections = self.split_text_by_sections(text_content)
                summary_contents = [
                    summary.project_background,
                    summary.product_requirements,
                    summary.core_functions,
                    summary.target_users
                ]
                
                # 进行相似度搜索以获取相似内容
                similar_texts = []
                for content in summary_contents:
                    if content.strip():
                        _, _, _, doc_name, doc_text = await self.local_similarity_search(content, current_sections)
                        if doc_text and doc_text != "无内容":
                            similar_texts.append(doc_text)
                
                if similar_texts:
                    similar_content = "\n\n".join(similar_texts)
                    similar_name = "相似文档"
                else:
                    similar_content = "暂无显著相似内容"
                    similar_name = "无相似文档"
            system_prompt = """
            你是一位专业的项目评审专家，擅长识别重复建设项目和项目差异化分析。请仔细分析以下两个项目文档。
            
            重复建设的判断标准：
            1. 项目目标和功能高度重叠
            2. 服务对象基本相同
            3. 技术方案相似度很高
            4. 可能造成资源浪费
            
            请严格按照以下格式输出分析结果：
            
            【判断结果】：[重复建设] 或 [非重复建设]
            
            【分析依据】：
            1. 项目背景和目标对比：[详细分析]
            2. 核心功能重叠度分析：[详细分析]
            3. 用户群体重叠情况：[详细分析]
            4. 技术方案相似性：[详细分析]
            5. 项目差异化特点：[详细分析]
            6. 重复建设风险评估：[详细分析]
            
            【最终结论】：
            基于以上分析，该项目[是/不是]重复建设项目。[详细说明判断理由]
            
            注意：
            - 必须严格按照上述格式输出
            - 【判断结果】部分只能是"重复建设"或"非重复建设"，不能有其他内容
            - 即使相似度较低，也请提供详细的对比分析
            - 重点说明项目的差异化特点和独特价值
            """
            
            # 根据相似内容情况调整分析提示
            if similar_content == "暂无显著相似内容":
                user_prompt = f"""
            当前项目：{current_name}
            项目内容：{current_content}
            
            对比情况：与现有项目库对比，未发现显著相似内容。
            
            请对该项目进行独立性分析，重点评估项目的创新性和独特价值，并严格按照指定格式输出分析结果。
            由于未发现相似项目，通常判断结果应为【非重复建设】。
            """
            else:
                user_prompt = f"""
            项目A：{current_name}
            内容：{current_content}
            
            项目B：{similar_name}
            内容：{similar_content}
            
            请对这两个项目进行重复建设风险分析，并严格按照指定格式输出分析结果。
            请特别注意【判断结果】部分必须明确标注"重复建设"或"非重复建设"。
            """
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = self.alibaba_client.chat.completions.create(
                model="qwen-plus-latest",
                messages=messages,
                temperature=0.3,
                max_tokens=3000,
                top_p=0.8
            )
            
            analysis_result = response.choices[0].message.content
            logger.info(f"大模型重复建设分析完成，分析长度: {len(analysis_result)}")
            logger.info(f"AI分析结果预览: {analysis_result[:200]}...")
            
            # 智能解析判断结果，优先提取结构化标识
            is_duplicate_construction = self._parse_ai_analysis_result(analysis_result)
            logger.info(f"最终解析结果: is_duplicate_construction = {is_duplicate_construction}")
            
            return {
                "is_duplicate_construction": is_duplicate_construction,
                "analysis_result": analysis_result,
                "analysis_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"大模型重复建设分析失败: {e}")
            return {
                "is_duplicate_construction": False,
                "analysis_result": f"分析失败: {str(e)}",
                "analysis_time": datetime.now().isoformat()
            }
    
    def _parse_ai_analysis_result(self, analysis_result: str) -> bool:
        """
        智能解析AI分析结果，优先提取结构化的判断结果标识
        """
        try:
            logger.info(f"开始解析AI分析结果，内容长度: {len(analysis_result)}")
            
            # 首先尝试提取结构化的【判断结果】标识
            import re
            
            # 查找【判断结果】标识
            judgment_pattern = r'【判断结果】[：:](.*?)(?=\n|【|$)'
            judgment_match = re.search(judgment_pattern, analysis_result)
            
            if judgment_match:
                judgment_text = judgment_match.group(1).strip()
                logger.info(f"提取到结构化判断结果: {judgment_text}")
                
                # 精确匹配判断结果
                if "重复建设" in judgment_text and "非重复建设" not in judgment_text:
                    logger.info("结构化解析：判断为重复建设")
                    return True
                elif "非重复建设" in judgment_text:
                    logger.info("结构化解析：判断为非重复建设")
                    return False
            
            # 如果没有找到结构化标识，尝试查找【最终结论】部分
            conclusion_pattern = r'【最终结论】[：:](.*?)(?=\n\n|【|$)'
            conclusion_match = re.search(conclusion_pattern, analysis_result, re.DOTALL)
            
            if conclusion_match:
                conclusion_text = conclusion_match.group(1).strip().lower()
                logger.info(f"提取到最终结论部分: {conclusion_text[:100]}...")
                
                # 在结论部分进行精确匹配
                if "不是重复建设" in conclusion_text or "非重复建设" in conclusion_text:
                    logger.info("结论部分解析：判断为非重复建设")
                    return False
                elif "是重复建设" in conclusion_text or "属于重复建设" in conclusion_text:
                    logger.info("结论部分解析：判断为重复建设")
                    return True
            
            # 回退到传统的关键词匹配方法
            logger.info("使用传统关键词匹配方法进行解析")
            analysis_lower = analysis_result.lower()
            
            # 更精确的关键词模式
            definitive_negative = [
                "该项目不是重复建设", "判断为非重复建设", "不构成重复建设",
                "非重复建设项目", "可以并行推进", "不存在重复建设风险"
            ]
            
            definitive_positive = [
                "该项目是重复建设", "判断为重复建设", "构成重复建设",
                "属于重复建设项目", "存在重复建设风险", "确认重复建设"
            ]
            
            # 优先检查明确的否定表述
            for pattern in definitive_negative:
                if pattern in analysis_lower:
                    logger.info(f"检测到明确否定模式: {pattern}")
                    return False
            
            # 检查明确的肯定表述
            for pattern in definitive_positive:
                if pattern in analysis_lower:
                    logger.info(f"检测到明确肯定模式: {pattern}")
                    return True
            
            # 如果都没有匹配到，进行更宽泛的匹配
            broad_negative = ["非重复建设", "不重复", "可并行"]
            broad_positive = ["重复建设", "重复项目"]
            
            negative_count = sum(1 for pattern in broad_negative if pattern in analysis_lower)
            positive_count = sum(1 for pattern in broad_positive if pattern in analysis_lower)
            
            logger.info(f"宽泛匹配 - 否定词数量: {negative_count}, 肯定词数量: {positive_count}")
            
            if negative_count > positive_count:
                return False
            elif positive_count > negative_count:
                return True
            else:
                # 默认为非重复建设（保守判断）
                logger.info("无法明确判断，默认为非重复建设")
                return False
                
        except Exception as e:
            logger.error(f"解析AI分析结果失败: {e}")
            # 出错时默认为非重复建设
            return False

    async def check_document_duplicate(self, document_id: str, file_path: str, document_name: str = None) -> DuplicateDetectionResult:
        """
        检查文档重复性（使用本地相似度计算）
        在检查前自动同步文档数据，确保只与有效文档进行对比
        """
        try:
            logger.info(f"开始检查文档 {document_id} 的重复性")
            
            # 在相似度检验前，自动同步并清理无效文档数据
            try:
                from services.document_service import document_service
                await document_service.initialize()  # 确保document_service已初始化
                existing_doc_ids = list(document_service.documents_db.keys())
                sync_result = self.sync_with_document_service(existing_doc_ids)
                logger.info(f"文档同步完成，清理了 {sync_result.get('removed_count', 0)} 个无效文档")
                
                # 如果document_chunks为空，尝试加载已存在的文档
                if not self.document_chunks:
                    logger.info("检测到document_chunks为空，开始加载已存在的文档")
                    loaded_count = await self.load_existing_documents_to_chunks()
                    logger.info(f"已加载 {loaded_count} 个已存在文档用于相似度对比")
                    
            except Exception as e:
                logger.warning(f"文档同步失败，继续进行相似度检测: {e}")
            
            # 提取文档文本
            text_content = self.extract_text_from_docx(file_path)
            
            # 按章节分割文档
            current_sections = self.split_text_by_sections(text_content)
            
            # 提取文档摘要
            summary = await self.extract_document_summary(text_content)
            
            # 检查四个维度的相似度
            summary_contents = [
                summary.project_background,
                summary.product_requirements,
                summary.core_functions,
                summary.target_users
            ]
            
            similarity_results = []
            doc_names = []
            doc_texts = []
            
            # 对每个摘要部分进行相似度检查
            for i, content in enumerate(summary_contents):
                if content.strip():
                    # 使用本地相似度搜索
                    semantic_sim, keyword_sim, structural_sim, doc_name, doc_text = await self.local_similarity_search(
                        content, current_sections
                    )
                    
                    # 综合相似度计算（语义40%，关键词40%，结构20%）
                    combined_similarity = (semantic_sim * 0.4 + keyword_sim * 0.4 + structural_sim * 0.2)
                    similarity_results.append(combined_similarity)
                    doc_names.append(doc_name)
                    doc_texts.append(doc_text)
                else:
                    similarity_results.append(0.0)
                    doc_names.append("无内容")
                    doc_texts.append("无内容")
            
            # 计算总体相似度
            total_similarity = sum(similarity_results) / len(similarity_results) if similarity_results else 0.0
            
            # 根据新的阈值策略判断相似度等级
            # 30%以下：正常，可直接导入
            # 30%-50%：警告状态，需要人工判断确认  
            # 50%以上：禁止导入，显示为重复项目
            is_duplicate = total_similarity >= self.block_threshold  # 50%以上视为重复
            needs_review = total_similarity >= self.warning_threshold  # 30%以上需要审核
            
            # 获取最相似的文档信息
            max_similarity_idx = similarity_results.index(max(similarity_results)) if similarity_results else 0
            duplicate_doc_name = doc_names[max_similarity_idx] if doc_names else None
            
            # 构建相似内容对比（返回完整内容，不省略）
            similar_content = []
            ai_analysis = None
            
            # 如果需要审核或重复，显示相似内容
            if needs_review:
                content_types = ["项目背景", "产品需求", "产品核心功能", "用户群体"]
                for i, (content_type, similarity) in enumerate(zip(content_types, similarity_results)):
                    if similarity >= self.warning_threshold:  # 30%以上显示相似内容
                        similar_content.append({
                            "type": content_type,
                            "current_content": summary_contents[i],  # 返回完整内容
                            "similar_content": doc_texts[i],  # 返回完整内容
                            "similarity_score": f"{float(similarity):.4f}"
                        })
                
            # 默认不进行AI分析，需要用户手动触发
            logger.info(f"相似度检测完成，相似度: {total_similarity:.2%}，AI分析需要用户手动触发")
            ai_analysis = None  # 默认不进行AI分析
            
            result = DuplicateDetectionResult(
                is_duplicate=is_duplicate,
                needs_review=needs_review,
                similarity_score=total_similarity,
                duplicate_document_name=duplicate_doc_name,
                similar_content=similar_content,
                detection_time=datetime.now(),
                ai_analysis=ai_analysis
            )
            
            # 存储当前文档用于后续比较（如果不是重复的且在文档管理系统中）
            if document_name and not is_duplicate and self._validate_document_exists(document_id):
                try:
                    await self.store_document_for_comparison(document_id, file_path, document_name)
                except Exception as e:
                    logger.warning(f"存储文档用于相似度比较失败，但不影响检测结果: {e}")
            elif document_name and not is_duplicate:
                logger.info(f"临时文档 {document_name} (ID: {document_id}) 不在文档管理系统中，跳过存储步骤")
            
            logger.info(f"文档 {document_id} 重复检测完成，相似度: {total_similarity:.2%}，是否重复: {is_duplicate}")
            
            return result
            
        except Exception as e:
            logger.error(f"文档重复检测失败: {e}")
            raise Exception(f"重复检测失败: {str(e)}")

# 创建全局服务实例
duplicate_detection_service = DuplicateDetectionService()