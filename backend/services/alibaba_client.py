import os
from openai import OpenAI
from typing import List, Dict, Any, Optional
from loguru import logger
from app.config import settings

class AlibabaClient:
    def __init__(self):
        self.api_key = settings.alibaba_api_key
        # 使用OpenAI兼容模式的base_url
        self.client = OpenAI(
            api_key=self.api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
    
    async def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        获取文本向量化结果
        阿里云API限制每批最多10个文本，需要分批处理
        """
        try:
            # 输入验证和清理
            if not texts:
                logger.warning("输入文本列表为空")
                return []
            
            # 过滤空文本和无效文本
            valid_texts = []
            for i, text in enumerate(texts):
                if text and isinstance(text, str) and text.strip():
                    valid_texts.append(text.strip())
                else:
                    logger.warning(f"跳过无效文本，索引: {i}, 内容: {repr(text)}")
            
            if not valid_texts:
                logger.warning("过滤后没有有效文本")
                return []
            
            logger.info(f"开始向量化处理，原始文本数: {len(texts)}, 有效文本数: {len(valid_texts)}")
            
            all_embeddings = []
            batch_size = 10  # 阿里云API严格限制每批最多10个文本
            total_batches = (len(valid_texts) + batch_size - 1) // batch_size
            
            # 将文本列表分批处理
            for i in range(0, len(valid_texts), batch_size):
                batch_texts = valid_texts[i:i + batch_size]
                batch_num = i // batch_size + 1
                
                # 严格验证批次大小
                if len(batch_texts) > batch_size:
                    logger.error(f"批次 {batch_num} 大小 {len(batch_texts)} 超过限制 {batch_size}")
                    raise Exception(f"批次大小 {len(batch_texts)} 超过API限制 {batch_size}")
                
                logger.info(f"处理第 {batch_num}/{total_batches} 批，包含 {len(batch_texts)} 个文本")
                logger.debug(f"批次 {batch_num} 文本长度: {[len(text) for text in batch_texts]}")
                
                # 调用API处理当前批次
                try:
                    response = self.client.embeddings.create(
                        model="text-embedding-v4",
                        input=batch_texts,
                        dimensions=1024,
                        encoding_format="float"
                    )
                    
                    # 验证响应
                    if not response.data:
                        logger.error(f"批次 {batch_num} API响应为空")
                        raise Exception(f"批次 {batch_num} API响应为空")
                    
                    if len(response.data) != len(batch_texts):
                        logger.error(f"批次 {batch_num} 响应数量 {len(response.data)} 与输入数量 {len(batch_texts)} 不匹配")
                        raise Exception(f"批次 {batch_num} 响应数量不匹配")
                    
                    # 从响应中提取当前批次的embedding
                    batch_embeddings = [item.embedding for item in response.data]
                    all_embeddings.extend(batch_embeddings)
                    
                    logger.info(f"批次 {batch_num} 处理成功，获得 {len(batch_embeddings)} 个向量")
                    
                except Exception as batch_error:
                    logger.error(f"批次 {batch_num} 处理失败: {batch_error}")
                    logger.error(f"批次 {batch_num} 详细信息 - 文本数量: {len(batch_texts)}, 文本预览: {[text[:50] + '...' if len(text) > 50 else text for text in batch_texts]}")
                    raise Exception(f"批次 {batch_num} 向量化失败: {str(batch_error)}")
            
            logger.info(f"向量化完成，成功获取 {len(all_embeddings)} 个向量，共处理 {total_batches} 批")
            
            # 最终验证
            if len(all_embeddings) != len(valid_texts):
                logger.error(f"最终向量数量 {len(all_embeddings)} 与有效文本数量 {len(valid_texts)} 不匹配")
                raise Exception("向量化结果数量验证失败")
            
            return all_embeddings
                
        except Exception as e:
            logger.error(f"获取向量化结果失败: {e}")
            raise Exception(f"向量化失败: {str(e)}")
    
    async def chat_completion(self, messages: List[Dict[str, str]], context: Optional[str] = None, document_references: Optional[List[str]] = None) -> str:
        """
        使用QWen模型进行问答
        """
        try:
            # 记录输入信息
            logger.info(f"开始生成回答，历史消息数: {len(messages)}")
            
            # 构建系统提示词
            system_prompt = """你是一个专业的文档问答助手。请尽力回答用户的问题。

重要指导原则：
1. 当用户提到特定文件名（如"附件1"、"附件2"、"技术方案"等）时，你应该理解这是对具体文档的引用
2. 如果用户要求总结某个特定文件，请基于该文件的内容进行详细总结
3. 优先使用提供的文档内容回答问题，如果文档内容不足，可以结合你的知识进行合理的补充和解释
4. 确保回答的准确性和相关性，提供有价值的信息
5. 回答要详细、专业且有帮助

文档别名说明：
- "附件1" 通常指代数字化项目技术方案相关文档
- "附件2" 通常指代数字化项目自评表相关文档
- "技术方案" 指代技术实施方案文档
- "自评表" 指代项目自评表文档"""
            
            if document_references:
                logger.info(f"用户提到了特定文档: {document_references}")
                system_prompt += f"\n\n用户在问题中提到了以下文档: {', '.join(document_references)}，请特别关注这些文档的内容。"
            
            if context:
                context_length = len(context)
                logger.info(f"使用文档上下文，长度: {context_length} 字符")
                logger.debug(f"上下文内容预览: {context[:200]}..." if context_length > 200 else f"上下文内容: {context}")
                system_prompt += f"\n\n相关文档内容：\n{context}"
            else:
                logger.info("未提供文档上下文，将基于通用知识回答")
            
            # 构建消息列表
            chat_messages = [
                {"role": "system", "content": system_prompt}
            ]
            chat_messages.extend(messages)
            
            total_input_length = sum(len(msg.get("content", "")) for msg in chat_messages)
            logger.info(f"总输入长度: {total_input_length} 字符，消息数: {len(chat_messages)}")
            
            response = self.client.chat.completions.create(
                model="qwen-plus",
                messages=chat_messages,
                temperature=0.7,
                max_tokens=2000,
                top_p=0.8
            )
            
            # 获取回答内容
            response_text = response.choices[0].message.content
            logger.info(f"成功获取回答，长度: {len(response_text)}")
            logger.debug(f"回答内容预览: {response_text[:100]}..." if len(response_text) > 100 else f"回答内容: {response_text}")
            return response_text
                
        except Exception as e:
            logger.error(f"问答生成失败: {e}")
            raise Exception(f"聊天生成失败: {str(e)}")
    
    async def test_connection(self) -> bool:
        """
        测试API连接
        """
        try:
            # 使用简单的向量化请求测试连接
            await self.get_embeddings(["测试连接"])
            logger.info("阿里云API连接测试成功")
            return True
        except Exception as e:
            logger.error(f"阿里云API连接测试失败: {e}")
            return False

# 创建全局客户端实例
alibaba_client = AlibabaClient()