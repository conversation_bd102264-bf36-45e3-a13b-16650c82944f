#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表格处理服务
用于处理Word文档中的表格，添加新列并导出为Excel
支持智能评估功能，根据自评标准判断表格数据
"""

import os
import re
import asyncio
import pandas as pd
from docx import Document
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill
from typing import List, Dict, Any, Optional
from pathlib import Path
import tempfile
from loguru import logger
from services.alibaba_client import alibaba_client
from services.chroma_client import chroma_client

class TableService:
    """
    表格处理服务类
    支持表格处理和智能评估功能
    """
    
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
        self.evaluation_cache = {}  # 缓存评估结果
        self.cache_lock = asyncio.Lock()  # 缓存操作锁，确保并发安全
        
        # 文档名称别名映射
        self.document_aliases = {
            "附件1": ["附件1", "附件一", "attachment1", "附件1数字化项目技术方案"],
            "附件2": ["附件2", "附件二", "attachment2", "附件2抚州市数字化项目自评表"],
            "技术方案": ["技术方案", "数字化项目技术方案", "智慧审批系统"],
            "自评表": ["自评表", "数字化项目自评表", "抚州市数字化项目自评表"]
        }
    
    def _extract_document_references(self, text: str) -> List[str]:
        """
        从表格行文本中提取文档引用
        
        Args:
            text: 表格行的文本内容
            
        Returns:
            引用的文档列表
        """
        referenced_docs = []
        text_lower = text.lower()
        
        # 检查每个别名组
        for doc_key, aliases in self.document_aliases.items():
            for alias in aliases:
                if alias.lower() in text_lower:
                    referenced_docs.append(doc_key)
                    logger.info(f"🎯 检测到文档引用: '{alias}' -> {doc_key}")
                    break
        
        # 使用正则表达式检测更多模式
        patterns = [
            r'附件\s*[1-9一二三四五六七八九十]',  # 附件1, 附件一等
            r'attachment\s*[1-9]',  # attachment1等
            r'文档\s*[1-9一二三四五六七八九十]',  # 文档1等
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                logger.info(f"🔍 正则匹配到文档引用: '{match}'")
                # 简单映射
                if '1' in match or '一' in match:
                    if "附件1" not in referenced_docs:
                        referenced_docs.append("附件1")
                elif '2' in match or '二' in match:
                    if "附件2" not in referenced_docs:
                        referenced_docs.append("附件2")
        
        return list(set(referenced_docs))  # 去重
    
    async def _search_documents_by_name(self, doc_references: List[str]) -> List[Dict[str, Any]]:
        """
        根据文档引用搜索特定文档
        
        Args:
            doc_references: 文档引用列表
            
        Returns:
            匹配的文档信息列表
        """
        if not doc_references:
            return []
        
        try:
            # 获取所有文档信息
            all_docs = await chroma_client.get_all_documents()
            matched_docs = []
            
            for doc_ref in doc_references:
                aliases = self.document_aliases.get(doc_ref, [doc_ref])
                
                for doc_id, doc_info in all_docs.items():
                    doc_name = doc_info.get('document_name', '')
                    
                    # 检查文档名是否匹配任何别名
                    for alias in aliases:
                        if alias.lower() in doc_name.lower():
                            matched_docs.append({
                                'document_id': doc_id,
                                'document_name': doc_name,
                                'reference': doc_ref,
                                'matched_alias': alias
                            })
                            logger.info(f"✅ 找到匹配文档: {doc_ref} -> {doc_name}")
                            break
            
            return matched_docs
            
        except Exception as e:
            logger.error(f"搜索文档失败: {e}")
            return []
    
    def extract_tables_from_docx(self, file_path: str) -> List[List[List[str]]]:
        """
        从Word文档中提取所有表格
        
        Args:
            file_path: Word文档路径
            
        Returns:
            表格数据列表，每个表格是一个二维列表
        """
        try:
            logger.info(f"开始提取Word文档表格: {file_path}")
            doc = Document(file_path)
            tables_data = []
            
            for table_idx, table in enumerate(doc.tables):
                logger.info(f"处理表格 {table_idx + 1}, 行数: {len(table.rows)}")
                table_data = []
                
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        row_data.append(cell_text)
                    table_data.append(row_data)
                
                tables_data.append(table_data)
                logger.info(f"表格 {table_idx + 1} 提取完成，共 {len(table_data)} 行")
            
            logger.info(f"文档表格提取完成，共提取 {len(tables_data)} 个表格")
            return tables_data
            
        except Exception as e:
            logger.error(f"提取表格时出错: {e}")
            raise
    
    def add_columns_to_table(self, table_data: List[List[str]], 
                           new_columns: List[str] = None) -> List[List[str]]:
        """
        为表格添加新列
        
        Args:
            table_data: 原始表格数据
            new_columns: 新列名列表，默认为['dp_out', 'dp_reason']
            
        Returns:
            添加新列后的表格数据
        """
        if new_columns is None:
            new_columns = ['dp_out', 'dp_reason']
        
        logger.info(f"为表格添加新列: {new_columns}")
        
        if not table_data:
            return table_data
        
        # 复制原始数据
        enhanced_table = []
        
        for row_idx, row in enumerate(table_data):
            new_row = row.copy()
            
            # 为表头添加新列名
            if row_idx == 0:
                new_row.extend(new_columns)
            else:
                # 为数据行添加空值
                new_row.extend([''] * len(new_columns))
            
            enhanced_table.append(new_row)
        
        logger.info(f"新列添加完成，表格列数从 {len(table_data[0]) if table_data else 0} 增加到 {len(enhanced_table[0]) if enhanced_table else 0}")
        return enhanced_table
    
    def add_columns_to_docx(self, file_path: str, output_path: str, 
                           new_columns: List[str] = None) -> str:
        """
        直接在Word文档中添加新列并保存为docx格式
        
        Args:
            file_path: 原始Word文档路径
            output_path: 输出文件路径
            new_columns: 新列名列表，默认为['dp_out', 'dp_reason']
            
        Returns:
            输出文件的路径
        """
        if new_columns is None:
            new_columns = ['dp_out', 'dp_reason']
            
        try:
            logger.info(f"开始处理Word文档: {file_path}")
            
            # 打开原始文档
            doc = Document(file_path)
            
            # 处理文档中的所有表格
            for table_idx, table in enumerate(doc.tables):
                logger.info(f"处理表格 {table_idx + 1}")
                
                # 获取当前表格的列数
                if len(table.rows) == 0:
                    continue
                    
                # 为每个新列添加列到表格
                for col_name in new_columns:
                    # 使用正确的python-docx API添加列
                    if len(table.rows) > 0:
                        # 获取当前列数
                        current_cols = len(table.rows[0].cells)
                        
                        # 为每一行添加新的单元格
                        for row in table.rows:
                            # 直接在XML层面添加新的tc元素
                            from docx.oxml import parse_xml
                            from docx.oxml.ns import nsdecls, qn
                            
                            # 创建新的tc元素
                            tc_xml = f'<w:tc {nsdecls("w")}><w:p><w:r><w:t></w:t></w:r></w:p></w:tc>'
                            tc_element = parse_xml(tc_xml)
                            
                            # 将新的tc元素添加到行中
                            row._tr.append(tc_element)
                        
                        # 设置表头文本
                        table.rows[0].cells[current_cols].text = col_name
                        
                        # 为其他行设置空值
                        for row_idx in range(1, len(table.rows)):
                            table.rows[row_idx].cells[current_cols].text = ""
                
                logger.info(f"表格 {table_idx + 1} 处理完成，添加了 {len(new_columns)} 列")
            
            # 保存修改后的文档
            doc.save(output_path)
            logger.info(f"Word文档保存成功: {output_path}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"处理Word文档时出错: {e}")
            raise
    
    def export_to_excel(self, table_data: List[List[str]], 
                       output_path: str, 
                       sheet_name: str = "数字化项目自评表") -> str:
        """
        将表格数据导出为Excel文件
        
        Args:
            table_data: 表格数据
            output_path: 输出文件路径
            sheet_name: 工作表名称
            
        Returns:
            导出文件的路径
        """
        try:
            logger.info(f"开始导出Excel文件: {output_path}")
            
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # 写入数据
            for row_idx, row_data in enumerate(table_data, 1):
                for col_idx, cell_value in enumerate(row_data, 1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=cell_value)
                    
                    # 设置表头样式
                    if row_idx == 1:
                        cell.font = Font(bold=True, color="FFFFFF")
                        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                        cell.alignment = Alignment(horizontal="center", vertical="center")
                    else:
                        cell.alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)
            
            # 调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                # 设置合适的列宽，最小15，最大50
                adjusted_width = min(max(max_length + 2, 15), 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # 保存文件
            wb.save(output_path)
            logger.info(f"Excel文件导出成功: {output_path}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"导出Excel文件时出错: {e}")
            raise
    
    async def process_document_table(self, file_path: str, 
                                   output_dir: str = None,
                                   new_columns: List[str] = None,
                                   enable_evaluation: bool = True,
                                   project_id: str = None,
                                   fill_existing_columns: bool = False) -> Dict[str, Any]:
        """
        处理文档表格的完整流程 - 直接在Word文档中添加列并保存为docx格式
        支持智能评估功能和项目范围限制
        
        Args:
            file_path: 文档文件路径
            output_dir: 输出目录，默认为临时目录
            new_columns: 新增列名列表
            enable_evaluation: 是否启用智能评估功能
            project_id: 项目ID，用于限制评估范围
            fill_existing_columns: 是否直接填充现有列（第3、4列）
            
        Returns:
            包含处理结果的字典
        """
        try:
            logger.info(f"开始处理文档表格: {file_path}，智能评估: {enable_evaluation}，项目ID: {project_id}，填充现有列: {fill_existing_columns}")
            
            if output_dir is None:
                output_dir = self.temp_dir
            
            if new_columns is None:
                new_columns = ['dp_out', 'dp_reason']
            
            # 生成输出文件名
            input_path = Path(file_path)
            output_filename = f"{input_path.stem}_processed.docx"
            output_path = os.path.join(output_dir, output_filename)
            
            if enable_evaluation:
                # 启用智能评估模式
                await self._process_with_evaluation(
                    file_path, output_path, new_columns, 
                    project_id=project_id, 
                    fill_existing_columns=fill_existing_columns
                )
            else:
                # 仅添加空列或复制文件
                if fill_existing_columns:
                    # 直接复制文件，不添加新列
                    import shutil
                    shutil.copy2(file_path, output_path)
                else:
                    self.add_columns_to_docx(file_path, output_path, new_columns)
            
            # 获取表格预览数据用于前端显示
            tables = self.extract_tables_from_docx(output_path)
            
            logger.info(f"文档表格处理完成: {output_path}")
            
            return {
                'success': True,
                'message': '表格处理成功' + ('（含智能评估）' if enable_evaluation else ''),
                'output_path': output_path,
                'output_filename': output_filename,
                'tables_count': len(tables) if tables else 0,
                'tables': tables[:1] if tables else [],  # 只返回第一个表格用于预览
                'evaluation_enabled': enable_evaluation
            }
            
        except Exception as e:
            logger.error(f"处理文档表格时出错: {e}")
            return {
                'success': False,
                'message': f'处理失败: {str(e)}',
                'tables': []
            }
    
    async def _process_with_evaluation(self, input_path: str, output_path: str, 
                                     new_columns: List[str], project_id: str = None,
                                     fill_existing_columns: bool = False):
        """
        带智能评估的表格处理
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            new_columns: 新增列名
            project_id: 项目ID，用于限制评估范围
            fill_existing_columns: 是否直接填充现有列
        """
        try:
            logger.info(f"🤖 开始智能评估模式处理")
            
            # 首先提取表格数据
            tables_data = self.extract_tables_from_docx(input_path)
            
            if not tables_data:
                logger.warning("文档中未找到表格")
                if fill_existing_columns:
                    import shutil
                    shutil.copy2(input_path, output_path)
                else:
                    self.add_columns_to_docx(input_path, output_path, new_columns)
                return
            
            # 打开原始文档
            doc = Document(input_path)
            
            # 处理每个表格
            for table_idx, (table_data, doc_table) in enumerate(zip(tables_data, doc.tables)):
                logger.info(f"📊 处理表格 {table_idx + 1}，共 {len(table_data)} 行")
                
                if len(table_data) < 2:  # 至少需要表头和一行数据
                    logger.warning(f"表格 {table_idx + 1} 数据不足，跳过评估")
                    continue
                
                headers = table_data[0]  # 表头
                
                # 根据模式处理列
                if fill_existing_columns:
                    # 项目评审模式：直接填充第3、4列
                    if len(headers) < 4:
                        logger.warning(f"表格 {table_idx + 1} 列数不足4列，跳过处理")
                        continue
                    target_col_indices = [2, 3]  # 第3、4列（0-based索引）
                else:
                    # 自评表评审模式：添加新列
                    self._add_columns_to_doc_table(doc_table, new_columns)
                    target_col_indices = [len(headers), len(headers) + 1]  # 新添加的列
                
                # 并发评估所有行数据（跳过表头）
                data_rows = [(table_data[row_idx], headers, row_idx) 
                           for row_idx in range(1, len(table_data))]
                
                if data_rows:
                    logger.info(f"🚀 开始并发评估 {len(data_rows)} 行数据")
                    
                    # 使用信号量控制并发数量，避免过多并发请求
                    semaphore = asyncio.Semaphore(5)  # 最多同时处理5行
                    
                    async def evaluate_with_semaphore(row_data, headers, row_idx):
                        async with semaphore:
                            logger.info(f"🔍 评估第 {row_idx} 行数据")
                            result = await self.evaluate_table_row(
                                row_data, headers, row_idx, project_id=project_id
                            )
                            logger.info(f"✅ 第 {row_idx} 行评估完成: {result['dp_out']}")
                            return row_idx, result
                    
                    # 并发执行所有行的评估
                    evaluation_tasks = [
                        evaluate_with_semaphore(row_data, headers, row_idx)
                        for row_data, headers, row_idx in data_rows
                    ]
                    
                    evaluation_results = await asyncio.gather(*evaluation_tasks, return_exceptions=True)
                    
                    # 处理评估结果
                    dp_out_col_idx = target_col_indices[0]  # dp_out列的索引
                    dp_reason_col_idx = target_col_indices[1]  # dp_reason列的索引
                    
                    for result in evaluation_results:
                        if isinstance(result, Exception):
                            logger.error(f"❌ 行评估出错: {result}")
                            continue
                            
                        row_idx, evaluation_result = result
                        
                        # 设置评估结果
                        doc_table.rows[row_idx].cells[dp_out_col_idx].text = evaluation_result['dp_out']
                        doc_table.rows[row_idx].cells[dp_reason_col_idx].text = evaluation_result['dp_reason']
                    
                    logger.info(f"🎉 并发评估完成，共处理 {len([r for r in evaluation_results if not isinstance(r, Exception)])} 行")
            
            # 保存处理后的文档
            doc.save(output_path)
            logger.info(f"🎉 智能评估处理完成，文件已保存: {output_path}")
            
        except Exception as e:
            logger.error(f"❌ 智能评估处理失败: {e}")
            # 回退到普通模式
            logger.info("回退到普通模式处理")
            if fill_existing_columns:
                import shutil
                shutil.copy2(input_path, output_path)
            else:
                self.add_columns_to_docx(input_path, output_path, new_columns)
    
    def _add_columns_to_doc_table(self, table, new_columns: List[str]):
        """
        为文档表格添加新列
        
        Args:
            table: python-docx表格对象
            new_columns: 新列名列表
        """
        try:
            if len(table.rows) == 0:
                return
            
            # 为每个新列添加列到表格
            for col_name in new_columns:
                # 获取当前列数
                current_cols = len(table.rows[0].cells)
                
                # 为每一行添加新的单元格
                for row in table.rows:
                    # 直接在XML层面添加新的tc元素
                    from docx.oxml import parse_xml
                    from docx.oxml.ns import nsdecls
                    
                    # 创建新的tc元素
                    tc_xml = f'<w:tc {nsdecls("w")}><w:p><w:r><w:t></w:t></w:r></w:p></w:tc>'
                    tc_element = parse_xml(tc_xml)
                    
                    # 将新的tc元素添加到行中
                    row._tr.append(tc_element)
                
                # 设置表头文本
                table.rows[0].cells[current_cols].text = col_name
                
                # 为其他行设置空值（将在评估时填充）
                for row_idx in range(1, len(table.rows)):
                    table.rows[row_idx].cells[current_cols].text = ""
                    
        except Exception as e:
            logger.error(f"添加表格列时出错: {e}")
            raise
    
    async def evaluate_table_row(self, row_data: List[str], headers: List[str], 
                                row_index: int, project_id: str = None) -> Dict[str, str]:
        """
        评估表格行数据是否符合标准
        
        Args:
            row_data: 行数据
            headers: 表头信息
            row_index: 行索引
            project_id: 项目ID，用于限制搜索范围
            
        Returns:
            包含dp_out和dp_reason的评估结果
        """
        try:
            logger.info(f"🔍 开始评估表格第{row_index}行数据")
            
            # 构建行数据描述
            row_description = ""
            for i, (header, value) in enumerate(zip(headers, row_data)):
                if value.strip():  # 只包含非空值
                    row_description += f"{header}: {value}; "
            
            if not row_description.strip():
                return {
                    "dp_out": "存疑",
                    "dp_reason": "该行数据为空或缺少关键信息"
                }
            
            logger.info(f"📝 行数据描述: {row_description[:100]}...")
            
            # 检查说明列和自评标准列的关联性
            correlation_result = await self._check_explanation_criteria_correlation(
                row_data, headers, row_index
            )
            if correlation_result:
                logger.info(f"⚠️ 第{row_index}行关联性检查未通过，直接返回预警")
                return correlation_result
            
            # 检查缓存（线程安全）
            cache_key = f"row_{row_index}_{hash(row_description)}"
            async with self.cache_lock:
                if cache_key in self.evaluation_cache:
                    logger.info(f"💾 使用缓存的评估结果")
                    return self.evaluation_cache[cache_key]
            
            # 构建评估查询
            evaluation_query = f"验证以下项目信息：{row_description}"
            
            # 获取查询向量
            query_embeddings = await alibaba_client.get_embeddings([evaluation_query])
            query_embedding = query_embeddings[0]
            
            # 检测表格行中是否提到了特定文档
            doc_references = self._extract_document_references(row_description)
            matched_docs = await self._search_documents_by_name(doc_references)
            
            # 搜索相关的标准文档
            if project_id:
                logger.info(f"🔍 在项目 {project_id} 中搜索相关标准文档")
            else:
                logger.info(f"🔍 搜索相关标准文档（全局范围）")
            
            # 构建搜索条件
            search_where = {}
            if project_id:
                search_where["project_id"] = project_id
            
            # 如果检测到特定文档引用，优先搜索这些文档
            if matched_docs:
                logger.info(f"🎯 表格行提到了特定文档，优先搜索: {[doc['reference'] for doc in matched_docs]}")
                
                # 为每个匹配的文档进行向量搜索
                all_ids, all_documents, all_metadatas, all_distances = [], [], [], []
                
                for matched_doc in matched_docs:
                    doc_id = matched_doc['document_id']
                    logger.info(f"🔍 在文档 '{matched_doc['document_name']}' 中搜索...")
                    
                    # 合并搜索条件
                    doc_search_where = {"document_id": doc_id}
                    if project_id:
                        doc_search_where["project_id"] = project_id
                    
                    doc_ids, doc_documents, doc_metadatas, doc_distances = await chroma_client.search_similar(
                        query_embedding=query_embedding,
                        n_results=10,  # 增加搜索结果数量
                        where=doc_search_where
                    )
                    
                    all_ids.extend(doc_ids)
                    all_documents.extend(doc_documents)
                    all_metadatas.extend(doc_metadatas)
                    all_distances.extend(doc_distances)
                    
                    logger.info(f"📊 在 '{matched_doc['document_name']}' 中找到 {len(doc_documents)} 个片段")
                
                # 如果特定文档搜索结果不足，补充项目范围内的搜索
                if len(all_documents) < 3:
                    if project_id:
                        logger.info(f"🔍 特定文档搜索结果不足，补充项目 {project_id} 内搜索...")
                    else:
                        logger.info(f"🔍 特定文档搜索结果不足，补充全局搜索...")
                    global_ids, global_documents, global_metadatas, global_distances = await chroma_client.search_similar(
                        query_embedding=query_embedding,
                        n_results=5,
                        where=search_where
                    )
                    
                    # 合并结果，去重
                    for i, global_id in enumerate(global_ids):
                        if global_id not in all_ids:
                            all_ids.append(global_id)
                            all_documents.append(global_documents[i])
                            all_metadatas.append(global_metadatas[i])
                            all_distances.append(global_distances[i])
                
                ids, documents, metadatas, distances = all_ids, all_documents, all_metadatas, all_distances
                
            else:
                # 常规向量搜索（在项目范围内）
                ids, documents, metadatas, distances = await chroma_client.search_similar(
                    query_embedding=query_embedding,
                    n_results=5,
                    where=search_where
                )
            
            # 构建验证上下文，包含材料来源信息
            context_parts = []
            source_info = []
            for i, (doc_content, metadata, distance) in enumerate(zip(documents, metadatas, distances)):
                similarity = max(0, 1 - distance)
                if similarity > 0.1:  # 较低的阈值以获取更多参考信息
                    # 获取文档来源信息
                    doc_name = metadata.get('document_name', f'文档{i+1}')
                    chunk_id = metadata.get('chunk_id', f'片段{i+1}')
                    
                    # 格式化材料内容，包含来源标识
                    formatted_content = f"【来源：{doc_name} - {chunk_id}】\n{doc_content}"
                    context_parts.append(formatted_content)
                    
                    # 记录来源信息用于日志
                    source_info.append({
                        'document': doc_name,
                        'chunk': chunk_id,
                        'similarity': f"{similarity:.3f}"
                    })
            
            # 记录检索到的材料信息
            if source_info:
                logger.info(f"📚 检索到{len(source_info)}个相关材料片段: {source_info}")
            
            # 检查是否找到相关材料
            if not context_parts:
                logger.warning(f"⚠️ 没有找到相关材料，直接返回未找到内容的结果")
                return {
                    "dp_out": "存疑",
                    "dp_reason": "在材料中未找到相应内容。"
                }
            
            context = "\n\n".join(context_parts)
            
            # 构建验证提示
            verification_prompt = f"""
请根据以下材料验证项目信息是否符合要求，并提供详细的分析过程：

参考材料：
{context}

项目信息：
{row_description}

**重要限制：只能基于提供的参考材料进行分析，如果材料中没有相关信息，必须直接回答"在材料中未找到相应内容"，不得进行任何扩展推理。**

请按以下格式进行详细分析并回答：

1. 材料检索分析：
   - 从参考材料中找到与项目信息相关的具体条款或要求
   - 引用具体的材料内容（如果有的话）

2. 对比分析过程：
   - 将项目信息与材料要求进行逐项对比
   - 分析符合程度和存在的差异

3. 判断依据：
   - 基于对比分析得出的具体理由
   - 指出关键的支持或冲突点

4. 最终结论：
   - 如果材料中有明确支持该信息的内容，输出："符合|[详细分析过程]"
   - 如果材料中没有相关信息或信息不明确，输出："存疑|在材料中未找到相应内容"
   - 如果材料中有与该信息冲突的内容，输出："不符合|[详细分析过程]"

请确保分析过程详细、逻辑清晰，用"|"分隔判断结果和详细分析过程。
"""
            
            # 调用AI进行验证
            logger.info(f"🤖 调用AI进行材料验证")
            verification_result = await alibaba_client.chat_completion(
                messages=[
                    {"role": "user", "content": verification_prompt}
                ],
                context=None
            )
            
            # 解析验证结果
            result = self._parse_verification_result(verification_result)
            
            # 缓存结果（线程安全）
            async with self.cache_lock:
                self.evaluation_cache[cache_key] = result
            
            logger.info(f"✅ 第{row_index}行验证完成: {result['dp_out']} - {result['dp_reason'][:50]}...")
            return result
            
        except Exception as e:
            logger.error(f"❌ 验证表格行数据时出错: {e}")
            return {
                "dp_out": "存疑",
                "dp_reason": f"验证过程出现错误: {str(e)}"
            }
    
    def _parse_verification_result(self, ai_response: str) -> Dict[str, str]:
        """
        解析AI验证结果，保留完整的详细分析过程
        优化解析逻辑，确保dp_out与最终结论保持一致
        
        Args:
            ai_response: AI返回的验证结果
            
        Returns:
            解析后的dp_out和dp_reason（包含完整分析过程）
        """
        try:
            logger.info(f"🔍 开始解析AI验证结果，响应长度: {len(ai_response)}")
            lines = ai_response.strip().split('\n')
            full_analysis = self._extract_full_analysis(ai_response)
            
            # 第一优先级：查找最终结论部分的明确判断
            logger.info("📋 第一优先级：搜索最终结论部分")
            conclusion_sections = []
            for i, line in enumerate(lines):
                line_clean = line.strip()
                if any(keyword in line_clean for keyword in ['最终结论', '4. 最终结论', '4.最终结论']):
                    # 收集最终结论部分的内容（当前行及后续几行）
                    conclusion_content = line_clean
                    for j in range(i + 1, min(i + 3, len(lines))):
                        if lines[j].strip():
                            conclusion_content += " " + lines[j].strip()
                    conclusion_sections.append(conclusion_content)
                    logger.info(f"🎯 找到最终结论部分: {conclusion_content[:100]}...")
            
            # 在最终结论部分查找明确的判断结果
            for conclusion in conclusion_sections:
                # 按优先级顺序检查判断结果，避免误判
                if '不符合' in conclusion and '符合' not in conclusion.replace('不符合', ''):
                    logger.info("✅ 最终结论解析: 不符合")
                    result = {
                        "dp_out": "不符合",
                        "dp_reason": full_analysis or ai_response
                    }
                    # 进行一致性验证
                    consistency_check = self._validate_parse_consistency(result["dp_out"], ai_response)
                    if not consistency_check["consistent"]:
                        logger.info(f"🔧 最终结论解析一致性修正: {consistency_check['correction_reason']}")
                        result["dp_out"] = consistency_check["dp_out"]
                        result["dp_reason"] += f"\n\n[系统修正]: {consistency_check['correction_reason']}"
                    return result
                elif '存疑' in conclusion:
                    logger.info("✅ 最终结论解析: 存疑")
                    result = {
                        "dp_out": "存疑",
                        "dp_reason": full_analysis or ai_response
                    }
                    # 进行一致性验证
                    consistency_check = self._validate_parse_consistency(result["dp_out"], ai_response)
                    if not consistency_check["consistent"]:
                        logger.info(f"🔧 最终结论解析一致性修正: {consistency_check['correction_reason']}")
                        result["dp_out"] = consistency_check["dp_out"]
                        result["dp_reason"] += f"\n\n[系统修正]: {consistency_check['correction_reason']}"
                    return result
                elif '符合' in conclusion:
                    logger.info("✅ 最终结论解析: 符合")
                    result = {
                        "dp_out": "符合",
                        "dp_reason": full_analysis or ai_response
                    }
                    # 进行一致性验证
                    consistency_check = self._validate_parse_consistency(result["dp_out"], ai_response)
                    if not consistency_check["consistent"]:
                        logger.info(f"🔧 最终结论解析一致性修正: {consistency_check['correction_reason']}")
                        result["dp_out"] = consistency_check["dp_out"]
                        result["dp_reason"] += f"\n\n[系统修正]: {consistency_check['correction_reason']}"
                    return result
            
            # 第二优先级：查找标准的管道分隔格式（符合|详细分析）
            logger.info("📋 第二优先级：搜索标准管道分隔格式")
            for line in lines:
                line_clean = line.strip().replace('**', '')
                if '|' in line_clean:
                    # 检查是否包含明确的判断关键词
                    for keyword in ['符合|', '存疑|', '不符合|']:
                        if keyword in line_clean:
                            parts = line_clean.split('|', 1)
                            if len(parts) == 2:
                                dp_out = parts[0].strip()
                                if dp_out in ['符合', '存疑', '不符合']:
                                    logger.info(f"✅ 管道格式解析: {dp_out}")
                                    detailed_reason = parts[1].strip()
                                    # 如果分析过程太简短，使用完整分析
                                    if '[详细分析过程]' in detailed_reason or len(detailed_reason) < 50:
                                        detailed_reason = full_analysis or detailed_reason
                                    
                                    result = {
                                        "dp_out": dp_out,
                                        "dp_reason": detailed_reason
                                    }
                                    
                                    # 进行一致性验证
                                    consistency_check = self._validate_parse_consistency(result["dp_out"], ai_response)
                                    if not consistency_check["consistent"]:
                                        logger.info(f"🔧 管道格式解析一致性修正: {consistency_check['correction_reason']}")
                                        result["dp_out"] = consistency_check["dp_out"]
                                        result["dp_reason"] += f"\n\n[系统修正]: {consistency_check['correction_reason']}"
                                    
                                    return result
            
            # 第三优先级：在整个响应中查找明确的判断关键词（谨慎处理）
            logger.info("📋 第三优先级：全文关键词搜索")
            response_text = ai_response.replace('\n', ' ')
            
            # 统计各种判断关键词的出现次数和上下文
            judgment_counts = {
                '不符合': response_text.count('不符合'),
                '存疑': response_text.count('存疑'),
                '符合': response_text.count('符合') - response_text.count('不符合')  # 排除"不符合"中的"符合"
            }
            
            logger.info(f"🔢 关键词统计: {judgment_counts}")
            
            # 查找最强的判断信号
            strong_negative_signals = ['冲突', '问题', '不满足', '缺少', '错误', '违反']
            strong_positive_signals = ['支持', '满足', '正确', '符合要求']
            
            has_strong_negative = any(signal in response_text for signal in strong_negative_signals)
            has_strong_positive = any(signal in response_text for signal in strong_positive_signals)
            
            logger.info(f"🚨 强负面信号: {has_strong_negative}, 强正面信号: {has_strong_positive}")
            
            # 基于综合分析做出判断
            if judgment_counts['不符合'] > 0 or has_strong_negative:
                logger.info("✅ 综合分析结果: 不符合")
                initial_result = {
                    "dp_out": "不符合",
                    "dp_reason": full_analysis or ai_response
                }
            elif judgment_counts['符合'] > 0 and has_strong_positive:
                logger.info("✅ 综合分析结果: 符合")
                initial_result = {
                    "dp_out": "符合",
                    "dp_reason": full_analysis or ai_response
                }
            else:
                logger.info("✅ 综合分析结果: 存疑（默认）")
                initial_result = {
                    "dp_out": "存疑",
                    "dp_reason": full_analysis or ai_response
                }
            
            # 进行一致性验证
            consistency_check = self._validate_parse_consistency(initial_result["dp_out"], ai_response)
            
            # 如果发现不一致并进行了修正，更新结果
            if not consistency_check["consistent"]:
                logger.info(f"🔧 应用一致性修正: {consistency_check['correction_reason']}")
                return {
                    "dp_out": consistency_check["dp_out"],
                    "dp_reason": initial_result["dp_reason"] + f"\n\n[系统修正]: {consistency_check['correction_reason']}"
                }
            
            return initial_result
                
        except Exception as e:
            logger.error(f"❌ 解析验证结果时出错: {e}")
            return {
                "dp_out": "存疑",
                "dp_reason": f"验证结果解析失败: {str(e)}"
            }
    
    def _validate_parse_consistency(self, dp_out: str, ai_response: str) -> Dict[str, str]:
        """
        验证解析结果的一致性，确保dp_out与AI响应中的最终结论保持一致
        
        Args:
            dp_out: 解析得到的判断结果
            ai_response: AI返回的完整响应
            
        Returns:
            验证后的结果，如果发现不一致会进行修正
        """
        try:
            logger.info(f"🔍 验证解析一致性，当前判断: {dp_out}")
            
            # 提取最终结论部分的文本
            lines = ai_response.strip().split('\n')
            conclusion_text = ""
            
            for i, line in enumerate(lines):
                if any(keyword in line for keyword in ['最终结论', '4. 最终结论', '4.最终结论']):
                    # 收集最终结论部分的完整文本
                    conclusion_lines = []
                    for j in range(i, min(i + 5, len(lines))):
                        if lines[j].strip():
                            conclusion_lines.append(lines[j].strip())
                    conclusion_text = ' '.join(conclusion_lines)
                    break
            
            if not conclusion_text:
                logger.info("📝 未找到明确的最终结论部分，跳过一致性验证")
                return {"dp_out": dp_out, "consistent": True}
            
            logger.info(f"📋 最终结论文本: {conclusion_text[:150]}...")
            
            # 检查最终结论中的明确表述
            conclusion_lower = conclusion_text.lower()
            
            # 定义不一致的情况
            inconsistencies = []
            
            if dp_out == "符合":
                if '不符合' in conclusion_text and '符合' not in conclusion_text.replace('不符合', ''):
                    inconsistencies.append("判断为符合，但最终结论提到不符合")
                elif any(neg in conclusion_text for neg in ['冲突', '问题', '错误', '违反']):
                    inconsistencies.append("判断为符合，但最终结论包含负面词汇")
            
            elif dp_out == "不符合":
                if '符合' in conclusion_text and '不符合' not in conclusion_text:
                    inconsistencies.append("判断为不符合，但最终结论只提到符合")
                elif any(pos in conclusion_text for pos in ['支持', '满足', '正确']):
                    inconsistencies.append("判断为不符合，但最终结论包含正面词汇")
            
            elif dp_out == "存疑":
                if '符合' in conclusion_text and '不符合' not in conclusion_text:
                    if any(pos in conclusion_text for pos in ['明确支持', '完全满足', '完全符合']):
                        inconsistencies.append("判断为存疑，但最终结论明确表示符合")
            
            # 如果发现不一致，尝试修正
            if inconsistencies:
                logger.warning(f"⚠️ 发现解析不一致: {inconsistencies}")
                
                # 基于最终结论重新判断
                if '不符合' in conclusion_text:
                    corrected_result = "不符合"
                elif '存疑' in conclusion_text:
                    corrected_result = "存疑"
                elif '符合' in conclusion_text:
                    corrected_result = "符合"
                else:
                    corrected_result = dp_out  # 保持原判断
                
                if corrected_result != dp_out:
                    logger.info(f"🔧 修正判断结果: {dp_out} -> {corrected_result}")
                    return {
                        "dp_out": corrected_result,
                        "consistent": False,
                        "correction_reason": f"原判断({dp_out})与最终结论不一致，已修正为({corrected_result})"
                    }
            
            logger.info("✅ 解析结果一致性验证通过")
            return {"dp_out": dp_out, "consistent": True}
            
        except Exception as e:
            logger.error(f"❌ 一致性验证时出错: {e}")
            return {"dp_out": dp_out, "consistent": True}  # 出错时保持原判断
    
    def _is_reference_explanation(self, explanation_content: str) -> bool:
        """
        检查说明内容是否为指引性说明
        
        Args:
            explanation_content: 说明内容
            
        Returns:
            如果是指引性说明返回True，否则返回False
        """
        # 指引性说明的常见模式
        reference_patterns = [
            r'见附件',
            r'见法规',
            r'见.*?法规',
            r'见文件',
            r'见规定',
            r'见标准',
            r'见要求',
            r'见通知',
            r'见办法',
            r'见细则',
            r'见条例',
            r'参见.*?附件',
            r'参见.*?法规',
            r'参见.*?文件',
            r'参考附件',
            r'参考法规',
            r'详见附件',
            r'详见法规',
            r'如附件',
            r'按附件',
            r'依据附件',
            r'根据附件',
            r'按照附件',
            r'遵照.*?附件',
            r'遵照.*?法规',
            r'执行附件',
            r'参照.*?附件',
            r'参照.*?法规',
            r'参照.*?标准',
            r'参照.*?要求'
        ]
        
        explanation_lower = explanation_content.lower()
        
        # 检查是否匹配任何指引性模式
        for pattern in reference_patterns:
            if re.search(pattern, explanation_content, re.IGNORECASE):
                logger.info(f"🔍 识别到指引性说明模式: '{pattern}' 在内容 '{explanation_content[:30]}...'")
                return True
        
        # 额外检查一些简短的指引性表述
        short_references = ['附件', '法规', '标准', '要求', '规定', '办法', '细则', '条例']
        words = explanation_content.strip().split()
        
        # 如果说明内容很短且只包含指引性词汇，也认为是指引性说明
        if len(words) <= 3:
            for word in words:
                if word in short_references:
                    logger.info(f"🔍 识别到简短指引性说明: '{explanation_content}'")
                    return True
        
        return False
    
    async def _check_explanation_criteria_correlation(self, row_data: List[str], 
                                                    headers: List[str], 
                                                    row_index: int) -> Dict[str, str]:
        """
        检查说明列和自评标准列的关联性
        
        Args:
            row_data: 行数据
            headers: 表头信息
            row_index: 行索引
            
        Returns:
            如果关联性不足返回预警结果，否则返回None
        """
        try:
            # 查找说明列和自评标准列的索引
            explanation_index = -1
            criteria_index = -1
            
            for i, header in enumerate(headers):
                if '说明' in header:
                    explanation_index = i
                elif '自评标准' in header:
                    criteria_index = i
            
            # 如果找不到这两列，跳过关联性检查
            if explanation_index == -1 or criteria_index == -1:
                logger.info(f"第{row_index}行未找到说明列或自评标准列，跳过关联性检查")
                return None
            
            # 获取说明和自评标准的内容
            if explanation_index >= len(row_data) or criteria_index >= len(row_data):
                logger.warning(f"第{row_index}行数据长度不足，跳过关联性检查")
                return None
                
            explanation_content = row_data[explanation_index].strip()
            criteria_content = row_data[criteria_index].strip()
            
            # 如果任一列为空，跳过检查
            if not explanation_content or not criteria_content:
                logger.info(f"第{row_index}行说明或自评标准为空，跳过关联性检查")
                return None
            
            # 检查是否为指引性说明，如果是则跳过关联性检查
            if self._is_reference_explanation(explanation_content):
                logger.info(f"第{row_index}行说明内容为指引性说明，跳过关联性检查")
                return None
            
            logger.info(f"🔗 检查第{row_index}行说明与自评标准的关联性")
            
            # 构建关联性检查提示
            correlation_prompt = f"""
请分析以下"说明"内容与"自评标准"的关联性：

自评标准：
{criteria_content}

说明内容：
{explanation_content}

请判断说明内容是否与自评标准存在明确的关联性：

1. 关联性分析：
   - 说明内容是否针对自评标准的要求进行了回应
   - 说明内容是否提供了与标准相关的具体信息、证据或解释
   - 说明内容是否与标准要求的主题、范围相匹配

2. 判断标准：
   - 强关联：说明内容直接回应标准要求，提供相关证据或解释
   - 弱关联：说明内容与标准有一定关系，但不够直接或具体
   - 无关联：说明内容与标准要求完全无关或答非所问

请按以下格式回答：
- 如果存在强关联或弱关联，回答："有关联"
- 如果完全无关联，回答："无关联|[具体分析原因]"
"""
            
            # 调用AI进行关联性分析
            correlation_result = await alibaba_client.chat_completion(
                messages=[
                    {"role": "user", "content": correlation_prompt}
                ],
                context=None
            )
            
            # 解析关联性检查结果
            if "无关联" in correlation_result:
                # 提取分析原因
                if "|" in correlation_result:
                    reason = correlation_result.split("|", 1)[1].strip()
                else:
                    reason = "说明内容与自评标准缺乏关联性"
                
                logger.warning(f"⚠️ 第{row_index}行关联性检查失败: {reason[:50]}...")
                
                return {
                    "dp_out": "预警",
                    "dp_reason": f"关联性检查：说明内容与自评标准缺乏关联性。{reason}"
                }
            
            logger.info(f"✅ 第{row_index}行关联性检查通过")
            return None
            
        except Exception as e:
            logger.error(f"❌ 关联性检查时出错: {e}")
            # 出错时不阻断正常流程，继续后续验证
            return None
    
    def _extract_full_analysis(self, ai_response: str) -> str:
        """
        提取完整的分析过程
        
        Args:
            ai_response: AI返回的完整响应
            
        Returns:
            格式化的完整分析过程
        """
        try:
            # 移除多余的空行和格式化文本
            lines = [line.strip() for line in ai_response.split('\n') if line.strip()]
            
            # 查找分析结构的关键部分
            analysis_sections = []
            current_section = ""
            
            for line in lines:
                # 检查是否是新的分析部分
                if any(keyword in line for keyword in ['1. 材料检索分析', '2. 对比分析过程', '3. 判断依据', '4. 最终结论']):
                    if current_section:
                        analysis_sections.append(current_section.strip())
                    current_section = line
                elif line and not line.startswith('请') and not line.startswith('根据'):
                    current_section += "\n" + line
            
            # 添加最后一个部分
            if current_section:
                analysis_sections.append(current_section.strip())
            
            # 如果找到了结构化的分析，返回格式化的内容
            if analysis_sections:
                return "\n\n".join(analysis_sections)
            
            # 否则返回清理后的原始内容
            return "\n".join(lines)
            
        except Exception as e:
            logger.error(f"提取分析过程时出错: {e}")
            return ai_response
    
    def get_table_preview(self, file_path: str, table_index: int = 0, 
                         max_rows: int = 10) -> Dict[str, Any]:
        """
        获取表格预览数据
        
        Args:
            file_path: Word文档路径
            table_index: 表格索引
            max_rows: 最大预览行数
            
        Returns:
            表格预览数据
        """
        try:
            tables = self.extract_tables_from_docx(file_path)
            
            if not tables or table_index >= len(tables):
                return {"success": False, "error": "表格不存在"}
            
            table_data = tables[table_index]
            preview_data = table_data[:max_rows]
            
            return {
                "success": True,
                "preview_data": preview_data,
                "total_rows": len(table_data),
                "total_columns": len(table_data[0]) if table_data else 0,
                "total_tables": len(tables)
            }
            
        except Exception as e:
            logger.error(f"获取表格预览时出错: {e}")
            return {"success": False, "error": str(e)}