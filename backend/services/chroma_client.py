import chromadb
from chromadb.config import Settings as ChromaSettings
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
import json
from app.config import settings
import uuid
import os
import time
import asyncio

class ChromaClient:
    def __init__(self):
        self.host = settings.chromadb_host
        self.port = settings.chromadb_port
        self.collection_name = settings.chromadb_collection_name
        self.client = None
        self.collection = None
        self._initialized = False
    
    async def initialize(self):
        """手动初始化ChromaDB客户端"""
        if not self._initialized:
            logger.info(f"🔄 开始初始化ChromaDB客户端，连接到 {self.host}:{self.port}")
            await asyncio.to_thread(self._initialize_client)
            self._initialized = True
            
            if self.client and self.collection:
                logger.info(f"✅ ChromaDB客户端初始化成功")
            else:
                logger.warning(f"⚠️ ChromaDB客户端初始化失败，client={self.client is not None}, collection={self.collection is not None}")
    
    def _initialize_client(self):
        """
        初始化ChromaDB客户端，使用内存模式
        """
        try:
            logger.info(f"初始化ChromaDB客户端（内存模式）...")
            
            # 使用持久化模式的ChromaDB客户端
            self.client = chromadb.PersistentClient(
                path="./chroma_db",
                settings=ChromaSettings(
                    allow_reset=True,
                    anonymized_telemetry=False
                )
            )
            
            # 获取或创建集合
            try:
                self.collection = self.client.get_collection(name=self.collection_name)
                logger.info(f"成功连接到现有集合: {self.collection_name}")
            except Exception:
                self.collection = self.client.create_collection(
                    name=self.collection_name,
                    metadata={"hnsw:space": "cosine"}
                )
                logger.info(f"成功创建新集合: {self.collection_name}")
            
            logger.info(f"ChromaDB客户端初始化成功（内存模式）")
            return
                
        except Exception as e:
            logger.error(f"ChromaDB初始化失败: {e}")
            # 不抛出异常，允许服务启动但ChromaDB功能不可用
            self.client = None
            self.collection = None
    
    async def add_documents(
        self, 
        document_id: str, 
        texts: List[str], 
        embeddings: List[List[float]], 
        metadatas: List[Dict[str, Any]] = None
    ) -> List[str]:
        """
        添加文档向量到数据库
        """
        try:
            logger.info(f"💾 ChromaDB存储开始 - 文档ID: {document_id}, 文本块数: {len(texts)}, 向量数: {len(embeddings)}")
            
            if not self.collection:
                logger.warning("⚠️ ChromaDB未初始化，返回模拟结果")
                return [f"{document_id}_{i}" for i in range(len(texts))]
            
            # 验证数据一致性
            if len(texts) != len(embeddings):
                raise ValueError(f"文本数量({len(texts)})与向量数量({len(embeddings)})不匹配")
            
            # 生成唯一ID
            ids = [f"{document_id}_{i}" for i in range(len(texts))]
            logger.info(f"📝 生成向量ID: {len(ids)} 个，格式: {document_id}_{{index}}")
            
            # 准备元数据
            if metadatas is None:
                metadatas = [{"document_id": document_id, "chunk_index": i} for i in range(len(texts))]
            
            # 记录存储前的集合状态
            before_count = self.collection.count()
            logger.info(f"📊 存储前集合包含 {before_count} 个向量")
            
            # 添加到集合
            logger.info(f"🚀 执行ChromaDB批量添加操作...")
            self.collection.add(
                embeddings=embeddings,
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
            
            # 验证存储结果
            after_count = self.collection.count()
            added_count = after_count - before_count
            
            logger.info(f"✅ 成功添加 {len(texts)} 个文档块到ChromaDB")
            logger.info(f"📊 存储后集合包含 {after_count} 个向量 (新增 {added_count} 个)")
            
            # 记录文档详情
            doc_name = metadatas[0].get('document_name', '未知文档') if metadatas else '未知文档'
            avg_text_length = sum(len(text) for text in texts) // len(texts)
            logger.info(f"📋 存储详情: 文档='{doc_name}', 平均文本长度={avg_text_length} 字符")
            
            return ids
            
        except Exception as e:
            logger.error(f"❌ 添加文档到ChromaDB失败: {e}")
            raise
    
    async def search_similar(
        self, 
        query_embedding: List[float], 
        n_results: int = 5,
        where: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[str], List[str], List[Dict[str, Any]], List[float]]:
        """
        搜索相似文档
        """
        try:
            logger.info(f"🔍 ChromaDB搜索开始 - 查询向量维度: {len(query_embedding)}, 请求结果数: {n_results}")
            
            if where:
                logger.info(f"📋 搜索过滤条件: {where}")
            
            if not self.collection:
                logger.warning("⚠️ ChromaDB未初始化，返回空结果")
                return [], [], [], []
            
            # 记录集合状态
            collection_count = self.collection.count()
            logger.info(f"📊 当前集合 '{self.collection_name}' 包含 {collection_count} 个向量")
            
            logger.info(f"🚀 执行ChromaDB向量搜索...")
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                where=where,
                include=["documents", "metadatas", "distances"]
            )
            
            if results and len(results["ids"]) > 0:
                ids = results["ids"][0]
                documents = results["documents"][0]
                metadatas = results["metadatas"][0]
                distances = results["distances"][0]
                
                logger.info(f"✅ ChromaDB搜索完成，返回 {len(documents)} 个结果")
                
                # 记录搜索结果详情
                if documents:
                    logger.info(f"📊 搜索结果统计:")
                    logger.info(f"  - 文档数量: {len(documents)}")
                    logger.info(f"  - 距离范围: {min(distances):.4f} ~ {max(distances):.4f}")
                    logger.info(f"  - 平均距离: {sum(distances)/len(distances):.4f}")
                    
                    # 记录前3个结果的详细信息
                    for i in range(min(3, len(documents))):
                        doc_name = metadatas[i].get('document_name', '未知文档')
                        chunk_idx = metadatas[i].get('chunk_index', 0)
                        content_preview = documents[i][:50] + "..." if len(documents[i]) > 50 else documents[i]
                        logger.info(f"  结果{i+1}: {doc_name}[块{chunk_idx}] 距离={distances[i]:.4f} 内容=\"{content_preview}\"")
                
                return ids, documents, metadatas, distances
            else:
                logger.warning(f"⚠️ ChromaDB搜索未返回任何结果")
                return [], [], [], []
                
        except Exception as e:
            logger.error(f"❌ ChromaDB搜索失败: {e}")
            raise
    
    async def delete_document(self, document_id: str) -> bool:
        """
        删除文档的所有向量
        """
        try:
            if not self.collection:
                logger.warning("ChromaDB未初始化，返回模拟结果")
                return True
            
            # 查找该文档的所有向量ID
            results = self.collection.get(
                where={"document_id": document_id},
                include=["metadatas"]
            )
            
            if results and len(results["ids"]) > 0:
                self.collection.delete(ids=results["ids"])
                logger.info(f"成功删除文档 {document_id} 的 {len(results['ids'])} 个向量")
                return True
            else:
                logger.warning(f"未找到文档 {document_id} 的向量")
                return False
                
        except Exception as e:
            logger.error(f"删除文档向量失败: {e}")
            raise
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """
        获取集合统计信息
        """
        try:
            if not self.collection:
                return {
                    "collection_name": self.collection_name,
                    "total_vectors": 0,
                    "status": "disabled"
                }
            
            count = self.collection.count()
            
            return {
                "collection_name": self.collection_name,
                "total_vectors": count,
                "status": "connected"
            }
            
        except Exception as e:
            logger.error(f"获取集合统计信息失败: {e}")
            return {
                "collection_name": self.collection_name,
                "total_vectors": 0,
                "status": "error",
                "error": str(e)
            }
    
    async def test_connection(self) -> bool:
        """
        测试ChromaDB连接
        """
        try:
            if self.collection:
                self.collection.count()
                return True
            return False
        except Exception as e:
            logger.error(f"ChromaDB连接测试失败: {e}")
            return False
    
    async def get_all_documents(self) -> Dict[str, Dict[str, Any]]:
        """
        获取ChromaDB中所有文档的信息
        """
        try:
            if not self.collection:
                logger.warning("ChromaDB未初始化，返回空结果")
                return {}
            
            # 获取所有向量的元数据
            results = self.collection.get(
                include=["metadatas"]
            )
            
            if not results or not results["ids"]:
                logger.info("ChromaDB中没有找到任何文档")
                return {}
            
            # 按文档ID分组整理文档信息
            documents_info = {}
            
            for i, metadata in enumerate(results["metadatas"]):
                document_id = metadata.get("document_id")
                if not document_id:
                    continue
                
                if document_id not in documents_info:
                    documents_info[document_id] = {
                        "document_id": document_id,
                        "document_name": metadata.get("document_name", "未知文档"),
                        "chunk_count": 0,
                        "created_time": metadata.get("created_time"),
                        "chunks": []
                    }
                
                documents_info[document_id]["chunk_count"] += 1
                documents_info[document_id]["chunks"].append({
                    "chunk_index": metadata.get("chunk_index", 0),
                    "chunk_length": metadata.get("chunk_length", 0)
                })
            
            logger.info(f"从ChromaDB获取到 {len(documents_info)} 个文档的信息")
            for doc_id, info in documents_info.items():
                logger.info(f"  文档: {info['document_name']} (ID: {doc_id[:8]}...) - {info['chunk_count']} 个块")
            
            return documents_info
            
        except Exception as e:
            logger.error(f"获取ChromaDB文档信息失败: {e}")
            return {}
    
    async def get_document_chunks(self, document_id: str) -> List[Dict[str, Any]]:
        """
        获取指定文档的所有分块信息
        """
        try:
            if not self.collection:
                logger.warning("ChromaDB未初始化，返回空结果")
                return []
            
            logger.info(f"🔍 获取文档 {document_id} 的所有分块信息")
            
            # 查找该文档的所有向量
            results = self.collection.get(
                where={"document_id": document_id},
                include=["documents", "metadatas"]
            )
            
            if not results or not results["ids"]:
                logger.warning(f"未找到文档 {document_id} 的分块信息")
                return []
            
            # 整理分块信息
            chunks = []
            for i, (chunk_id, document, metadata) in enumerate(zip(
                results["ids"], results["documents"], results["metadatas"]
            )):
                chunk_info = {
                    "id": chunk_id,
                    "chunk_index": metadata.get("chunk_index", i),
                    "content": document,
                    "content_length": len(document),
                    "metadata": metadata,
                    "created_time": metadata.get("created_time", "")
                }
                chunks.append(chunk_info)
            
            # 按chunk_index排序
            chunks.sort(key=lambda x: x["chunk_index"])
            
            logger.info(f"✅ 成功获取文档 {document_id} 的 {len(chunks)} 个分块")
            return chunks
            
        except Exception as e:
            logger.error(f"获取文档分块失败: {e}")
            raise
    
    async def clear_collection(self) -> bool:
        """
        清空集合中的所有数据
        """
        try:
            if not self.collection:
                logger.warning("ChromaDB未初始化，返回模拟结果")
                return True
            
            # 获取所有ID并删除
            results = self.collection.get(include=[])
            if results and len(results["ids"]) > 0:
                self.collection.delete(ids=results["ids"])
                logger.info(f"成功清空集合，删除了 {len(results['ids'])} 个向量")
            
            return True
            
        except Exception as e:
            logger.error(f"清空集合失败: {e}")
            raise

# 创建全局客户端实例
chroma_client = ChromaClient()