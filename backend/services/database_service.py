#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接和会话管理服务
提供PostgreSQL数据库的连接池管理、会话管理和基础操作
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional, Dict, Any, List
from sqlalchemy.ext.asyncio import (
    create_async_engine,
    AsyncEngine,
    AsyncSession,
    async_sessionmaker
)
from sqlalchemy.orm import DeclarativeBase, sessionmaker
from sqlalchemy import (
    create_engine,
    Engine,
    text,
    MetaData,
    inspect
)
from sqlalchemy.pool import QueuePool
from loguru import logger
from app.config import settings


class Base(DeclarativeBase):
    """SQLAlchemy声明式基类"""
    pass


class DatabaseService:
    """
    数据库服务类
    管理PostgreSQL连接池、会话和基础操作
    """
    
    def __init__(self):
        self._async_engine: Optional[AsyncEngine] = None
        self._sync_engine: Optional[Engine] = None
        self._async_session_factory: Optional[async_sessionmaker] = None
        self._sync_session_factory: Optional[sessionmaker] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """
        初始化数据库连接
        """
        if self._initialized:
            return
        
        try:
            logger.info("正在初始化数据库连接...")
            
            # 创建异步引擎
            self._async_engine = create_async_engine(
                settings.postgres_url,
                pool_size=settings.postgres_pool_size,
                max_overflow=settings.postgres_max_overflow,
                pool_timeout=settings.postgres_pool_timeout,
                pool_recycle=settings.postgres_pool_recycle,
                pool_pre_ping=True,
                echo=settings.debug,
                future=True
            )
            
            # 创建同步引擎（用于迁移和管理操作）
            self._sync_engine = create_engine(
                settings.postgres_sync_url,
                poolclass=QueuePool,
                pool_size=5,
                max_overflow=10,
                pool_timeout=30,
                pool_recycle=3600,
                pool_pre_ping=True,
                echo=settings.debug,
                future=True
            )
            
            # 创建会话工厂
            self._async_session_factory = async_sessionmaker(
                bind=self._async_engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False
            )
            
            self._sync_session_factory = sessionmaker(
                bind=self._sync_engine,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False
            )
            
            # 测试连接
            await self.test_connection()
            
            self._initialized = True
            logger.info("数据库连接初始化成功")
            
        except Exception as e:
            logger.error(f"数据库连接初始化失败: {e}")
            raise
    
    async def close(self) -> None:
        """
        关闭数据库连接
        """
        if self._async_engine:
            await self._async_engine.dispose()
            logger.info("异步数据库引擎已关闭")
        
        if self._sync_engine:
            self._sync_engine.dispose()
            logger.info("同步数据库引擎已关闭")
        
        self._initialized = False
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        获取异步数据库会话（上下文管理器）
        
        使用示例:
        async with database_service.get_session() as session:
            result = await session.execute(text("SELECT 1"))
        """
        if not self._initialized:
            await self.initialize()
        
        if not self._async_session_factory:
            raise RuntimeError("数据库会话工厂未初始化")
        
        session = self._async_session_factory()
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error(f"数据库会话错误: {e}")
            raise
        finally:
            await session.close()
    
    def get_sync_session(self):
        """
        获取同步数据库会话（用于迁移等操作）
        """
        if not self._sync_session_factory:
            raise RuntimeError("同步数据库会话工厂未初始化")
        
        return self._sync_session_factory()
    
    async def test_connection(self) -> bool:
        """
        测试数据库连接
        """
        try:
            async with self.get_session() as session:
                result = await session.execute(text("SELECT 1 as test"))
                row = result.fetchone()
                if row and row.test == 1:
                    logger.info("数据库连接测试成功")
                    return True
                else:
                    logger.error("数据库连接测试失败：返回值不正确")
                    return False
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False
    
    async def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行查询语句
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果列表
        """
        try:
            async with self.get_session() as session:
                result = await session.execute(text(query), params or {})
                columns = result.keys()
                rows = result.fetchall()
                
                return [
                    dict(zip(columns, row))
                    for row in rows
                ]
        except Exception as e:
            logger.error(f"执行查询失败: {e}")
            raise
    
    async def execute_command(self, command: str, params: Optional[Dict[str, Any]] = None) -> int:
        """
        执行命令语句（INSERT, UPDATE, DELETE）
        
        Args:
            command: SQL命令语句
            params: 命令参数
            
        Returns:
            影响的行数
        """
        try:
            async with self.get_session() as session:
                result = await session.execute(text(command), params or {})
                return result.rowcount
        except Exception as e:
            logger.error(f"执行命令失败: {e}")
            raise
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """
        获取数据库统计信息
        """
        try:
            stats_query = """
            SELECT 
                metric_name,
                metric_value,
                description
            FROM get_database_performance_stats()
            """
            
            stats_result = await self.execute_query(stats_query)
            
            # 获取表大小信息
            table_size_query = """
            SELECT 
                table_name,
                row_count,
                table_size,
                index_size,
                total_size
            FROM get_table_sizes()
            LIMIT 10
            """
            
            table_sizes = await self.execute_query(table_size_query)
            
            return {
                "performance_stats": stats_result,
                "table_sizes": table_sizes,
                "connection_info": {
                    "host": settings.postgres_host,
                    "port": settings.postgres_port,
                    "database": settings.postgres_db,
                    "pool_size": settings.postgres_pool_size,
                    "max_overflow": settings.postgres_max_overflow
                }
            }
        except Exception as e:
            logger.error(f"获取数据库统计信息失败: {e}")
            return {"error": str(e)}
    
    async def check_table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在
        
        Args:
            table_name: 表名
            
        Returns:
            表是否存在
        """
        try:
            query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = :table_name
            )
            """
            
            result = await self.execute_query(query, {"table_name": table_name})
            return result[0]["exists"] if result else False
        except Exception as e:
            logger.error(f"检查表存在性失败: {e}")
            return False
    
    async def get_table_info(self, table_name: str) -> Optional[Dict[str, Any]]:
        """
        获取表信息
        
        Args:
            table_name: 表名
            
        Returns:
            表信息字典
        """
        try:
            # 获取表结构信息
            columns_query = """
            SELECT 
                column_name,
                data_type,
                is_nullable,
                column_default,
                character_maximum_length
            FROM information_schema.columns
            WHERE table_schema = 'public' 
            AND table_name = :table_name
            ORDER BY ordinal_position
            """
            
            columns = await self.execute_query(columns_query, {"table_name": table_name})
            
            # 获取索引信息
            indexes_query = """
            SELECT 
                indexname,
                indexdef
            FROM pg_indexes
            WHERE schemaname = 'public'
            AND tablename = :table_name
            """
            
            indexes = await self.execute_query(indexes_query, {"table_name": table_name})
            
            return {
                "table_name": table_name,
                "columns": columns,
                "indexes": indexes
            }
        except Exception as e:
            logger.error(f"获取表信息失败: {e}")
            return None
    
    @property
    def async_engine(self) -> Optional[AsyncEngine]:
        """获取异步引擎"""
        return self._async_engine
    
    @property
    def sync_engine(self) -> Optional[Engine]:
        """获取同步引擎"""
        return self._sync_engine
    
    @property
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self._initialized


# 创建全局数据库服务实例
database_service = DatabaseService()


# 应用启动和关闭时的数据库管理
async def init_database():
    """应用启动时初始化数据库"""
    await database_service.initialize()


async def close_database():
    """应用关闭时关闭数据库连接"""
    await database_service.close()