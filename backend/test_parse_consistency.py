#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试解析一致性修复功能
验证_parse_verification_result方法是否能正确处理dp_out与最终结论不一致的问题
"""

import sys
import os
import asyncio
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.table_service import TableService
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ParseConsistencyTester:
    """解析一致性测试器"""
    
    def __init__(self):
        self.table_service = TableService()
        self.test_cases = self._create_test_cases()
    
    def _create_test_cases(self) -> list:
        """创建测试用例"""
        return [
            {
                "name": "最终结论符合但解析为不符合的情况",
                "ai_response": """
1. 材料检索分析：
   - 从参考材料中找到项目资金来源的相关要求
   - 材料明确规定项目需要有明确的资金来源证明

2. 对比分析过程：
   - 项目信息显示资金来源为财政拨款500万元
   - 材料要求与项目实际情况进行对比
   - 发现项目资金来源明确且已到位

3. 判断依据：
   - 项目有明确的资金来源
   - 资金已经到位
   - 符合材料中的相关要求

4. 最终结论：
   - 项目资金来源符合要求，资金已到位，满足相关标准

不符合|项目存在资金问题
                """,
                "expected_dp_out": "符合",
                "description": "AI响应中最终结论明确表示符合，但管道格式错误地标记为不符合"
            },
            {
                "name": "最终结论不符合但解析为符合的情况",
                "ai_response": """
1. 材料检索分析：
   - 从参考材料中找到技术方案的相关要求
   - 材料要求项目必须采用先进的技术架构

2. 对比分析过程：
   - 项目信息显示采用传统单体架构
   - 材料要求采用微服务或分布式架构
   - 存在明显的技术架构冲突

3. 判断依据：
   - 项目技术方案与材料要求不匹配
   - 存在技术架构方面的问题
   - 不满足先进性要求

4. 最终结论：
   - 项目技术方案不符合材料要求，存在技术架构冲突

符合|项目技术方案满足要求
                """,
                "expected_dp_out": "不符合",
                "description": "AI响应中最终结论明确表示不符合，但管道格式错误地标记为符合"
            },
            {
                "name": "最终结论存疑但解析为符合的情况",
                "ai_response": """
1. 材料检索分析：
   - 在参考材料中未找到明确的相关要求
   - 材料内容与项目信息关联度较低

2. 对比分析过程：
   - 项目信息提供的内容有限
   - 无法从材料中找到对应的验证标准
   - 缺乏足够的对比依据

3. 判断依据：
   - 材料中缺乏相关信息
   - 无法进行有效的符合性判断
   - 需要更多补充材料

4. 最终结论：
   - 由于材料中未找到相应内容，无法确定是否符合要求，建议存疑处理

符合|项目完全满足所有要求
                """,
                "expected_dp_out": "存疑",
                "description": "AI响应中最终结论建议存疑处理，但管道格式错误地标记为符合"
            },
            {
                "name": "一致的符合情况",
                "ai_response": """
1. 材料检索分析：
   - 从参考材料中找到明确的项目要求
   - 材料详细规定了项目的各项标准

2. 对比分析过程：
   - 项目信息与材料要求完全匹配
   - 所有关键指标都满足标准
   - 无发现任何冲突或问题

3. 判断依据：
   - 项目完全符合材料要求
   - 所有标准都得到满足
   - 具备充分的支持依据

4. 最终结论：
   - 项目完全符合材料要求，满足所有相关标准

符合|项目完全满足材料中的所有要求和标准
                """,
                "expected_dp_out": "符合",
                "description": "AI响应中最终结论和管道格式都正确标记为符合"
            },
            {
                "name": "复杂的不一致情况",
                "ai_response": """
1. 材料检索分析：
   项目在某些方面符合要求，但在关键技术指标上存在不足。

2. 对比分析过程：
   发现项目在资金和人员配置方面符合标准，但技术方案存在重大缺陷。

3. 判断依据：
   虽然部分指标达标，但关键技术问题导致整体不符合要求。

4. 最终结论：
   综合考虑各方面因素，项目整体不符合材料要求，主要问题在于技术方案的缺陷。

符合|项目各方面都表现良好，完全满足要求
                """,
                "expected_dp_out": "不符合",
                "description": "复杂情况：最终结论明确表示不符合，但管道格式标记为符合"
            }
        ]
    
    def run_test_case(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """运行单个测试用例"""
        logger.info(f"\n🧪 测试用例: {test_case['name']}")
        logger.info(f"📝 描述: {test_case['description']}")
        logger.info(f"🎯 期望结果: {test_case['expected_dp_out']}")
        
        try:
            # 调用解析方法
            result = self.table_service._parse_verification_result(test_case['ai_response'])
            
            # 检查结果
            actual_dp_out = result['dp_out']
            is_correct = actual_dp_out == test_case['expected_dp_out']
            
            logger.info(f"✅ 实际结果: {actual_dp_out}")
            logger.info(f"🔍 测试结果: {'通过' if is_correct else '失败'}")
            
            if not is_correct:
                logger.warning(f"⚠️ 期望 {test_case['expected_dp_out']}，但得到 {actual_dp_out}")
            
            # 检查是否包含系统修正信息
            has_correction = '[系统修正]' in result['dp_reason']
            if has_correction:
                logger.info("🔧 检测到系统修正")
            
            return {
                'test_name': test_case['name'],
                'expected': test_case['expected_dp_out'],
                'actual': actual_dp_out,
                'passed': is_correct,
                'has_correction': has_correction,
                'reason': result['dp_reason'][:200] + '...' if len(result['dp_reason']) > 200 else result['dp_reason']
            }
            
        except Exception as e:
            logger.error(f"❌ 测试用例执行失败: {e}")
            return {
                'test_name': test_case['name'],
                'expected': test_case['expected_dp_out'],
                'actual': 'ERROR',
                'passed': False,
                'has_correction': False,
                'error': str(e)
            }
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试用例"""
        logger.info("🚀 开始解析一致性测试")
        logger.info(f"📊 总测试用例数: {len(self.test_cases)}")
        
        results = []
        passed_count = 0
        correction_count = 0
        
        for test_case in self.test_cases:
            result = self.run_test_case(test_case)
            results.append(result)
            
            if result['passed']:
                passed_count += 1
            if result.get('has_correction', False):
                correction_count += 1
        
        # 生成测试报告
        total_tests = len(self.test_cases)
        pass_rate = (passed_count / total_tests) * 100 if total_tests > 0 else 0
        
        logger.info("\n📋 测试报告")
        logger.info(f"📊 总测试数: {total_tests}")
        logger.info(f"✅ 通过数: {passed_count}")
        logger.info(f"❌ 失败数: {total_tests - passed_count}")
        logger.info(f"🔧 系统修正数: {correction_count}")
        logger.info(f"📈 通过率: {pass_rate:.1f}%")
        
        # 详细结果
        logger.info("\n📝 详细结果:")
        for result in results:
            status = "✅" if result['passed'] else "❌"
            correction = " [已修正]" if result.get('has_correction', False) else ""
            logger.info(f"{status} {result['test_name']}: {result['expected']} -> {result['actual']}{correction}")
        
        return {
            'total_tests': total_tests,
            'passed': passed_count,
            'failed': total_tests - passed_count,
            'corrections': correction_count,
            'pass_rate': pass_rate,
            'results': results
        }

def main():
    """主函数"""
    try:
        tester = ParseConsistencyTester()
        report = tester.run_all_tests()
        
        # 判断测试是否成功
        if report['pass_rate'] >= 80:  # 80%以上通过率认为成功
            logger.info("\n🎉 解析一致性测试整体通过！")
            return 0
        else:
            logger.error("\n💥 解析一致性测试失败，需要进一步优化")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 测试执行出错: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)