#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试：验证关联性检查功能中的指引性说明识别
测试完整的关联性检查流程，确保指引性说明能够正确跳过预警
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.table_service import TableService
from loguru import logger

async def test_correlation_check_with_directive_explanations():
    """
    测试关联性检查功能对指引性说明的处理
    """
    logger.info("🧪 开始集成测试：关联性检查中的指引性说明处理")
    
    # 初始化表格服务
    table_service = TableService()
    
    # 测试用例：包含指引性说明的表格行
    test_cases = [
        {
            "name": "见附件类型",
            "headers": ["自评标准", "说明", "材料"],
            "row_data": ["技术标准A", "见附件1", "技术文档"],
            "should_skip": True
        },
        {
            "name": "见法规类型",
            "headers": ["自评标准", "说明", "材料"],
            "row_data": ["安全标准B", "见相关法规", "安全文档"],
            "should_skip": True
        },
        {
            "name": "参见相关法规类型",
            "headers": ["自评标准", "说明", "材料"],
            "row_data": ["质量标准C", "参见相关法规执行", "质量文档"],
            "should_skip": True
        },
        {
            "name": "正常说明类型",
            "headers": ["自评标准", "说明", "材料"],
            "row_data": ["环保标准D", "需要满足国家环保要求，确保污染物排放达标", "环保文档"],
            "should_skip": False
        },
        {
            "name": "空说明类型",
            "headers": ["自评标准", "说明", "材料"],
            "row_data": ["测试标准E", "", "测试文档"],
            "should_skip": True  # 空说明也应该跳过
        }
    ]
    
    results = []
    
    for i, case in enumerate(test_cases):
        logger.info(f"\n📋 测试用例: {case['name']}")
        logger.info(f"   表头: {case['headers']}")
        logger.info(f"   行数据: {case['row_data']}")
        
        try:
            # 调用关联性检查方法
            result = await table_service._check_explanation_criteria_correlation(
                case['row_data'], 
                case['headers'], 
                i + 1  # 行索引从1开始
            )
            
            # 检查结果
            if case['should_skip']:
                # 应该跳过的情况，期望返回None
                if result is None:
                    logger.success(f"✅ {case['name']}: 正确跳过关联性检查")
                    results.append(True)
                else:
                    logger.error(f"❌ {case['name']}: 应该跳过但没有跳过，结果: {result}")
                    results.append(False)
            else:
                # 不应该跳过的情况，期望进行正常检查
                # 正常检查可能返回None（通过）或dict（有问题）
                if result is None:
                    # 返回None表示检查通过，没有问题
                    logger.success(f"✅ {case['name']}: 正确进行关联性检查并通过")
                    results.append(True)
                elif isinstance(result, dict) and result.get('dp_out') == '预警':
                    # 返回预警结果也是正常的检查流程
                    logger.success(f"✅ {case['name']}: 正确进行关联性检查并发现问题")
                    results.append(True)
                else:
                    logger.error(f"❌ {case['name']}: 检查结果异常: {result}")
                    results.append(False)
                    
        except Exception as e:
            logger.error(f"❌ {case['name']}: 测试过程中出现异常: {str(e)}")
            results.append(False)
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    success_rate = (passed / total) * 100 if total > 0 else 0
    
    logger.info(f"\n=== 集成测试结果统计 ===")
    logger.info(f"通过测试: {passed}/{total} ({success_rate:.1f}%)")
    
    if passed == total:
        logger.success("🎉 所有集成测试都通过了！")
        return True
    else:
        logger.error(f"💥 有 {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_correlation_check_with_directive_explanations())
    sys.exit(0 if success else 1)