from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    # 阿里云百炼API配置
    alibaba_api_key: str
    alibaba_base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    # PostgreSQL数据库配置
    postgres_host: str = "localhost"
    postgres_port: int = 5432
    postgres_db: str = "doc_qa_system"
    postgres_user: str = "doc_qa_user"
    postgres_password: str = "doc_qa_password_2024"
    postgres_pool_size: int = 10
    postgres_max_overflow: int = 20
    postgres_pool_timeout: int = 30
    postgres_pool_recycle: int = 3600
    
    # ChromaDB配置
    chromadb_host: str = "localhost"
    chromadb_port: int = 8000
    chromadb_collection_name: str = "documents"
    
    # 应用配置
    app_host: str = "0.0.0.0"
    app_port: int = 8000
    debug: bool = True
    
    # 文件存储配置
    upload_dir: str = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "documents"))
    max_file_size: int = 52428800  # 50MB
    
    # 日志配置
    log_level: str = "INFO"
    
    # CORS配置
    cors_origins: list = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    @property
    def postgres_url(self) -> str:
        """生成PostgreSQL连接URL"""
        return f"postgresql+asyncpg://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
    
    @property
    def postgres_sync_url(self) -> str:
        """生成PostgreSQL同步连接URL"""
        return f"postgresql://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

# 创建全局设置实例
settings = Settings()

# 确保上传目录存在
os.makedirs(settings.upload_dir, exist_ok=True)