# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
PYTHONPATH

# 虚拟环境
venv/
env/
ENV/
.venv/
.env/

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# 测试文件
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 数据库文件
*.db
*.sqlite3

# 上传文件（开发时的临时文件）
uploads/temp/
uploads/cache/

# ChromaDB数据
chroma_db/

# 环境变量文件（保留.env用于容器配置）
# .env

# Git相关
.git/
.gitignore

# Docker相关
Dockerfile*
.dockerignore
docker-compose*.yml

# 文档
*.md
README*

# 备份文件
*.bak
*.backup

# 临时文件
*.tmp
*.temp

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini