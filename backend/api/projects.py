from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse
from typing import Optional
from loguru import logger
from models.schemas import (
    Project, ProjectCreateRequest, ProjectUpdateRequest, 
    ProjectResponse, ProjectListResponse, ErrorResponse
)
from services.project_service import project_service

router = APIRouter(prefix="/api/projects", tags=["projects"])

@router.post("", response_model=ProjectResponse)
async def create_project(request: ProjectCreateRequest):
    """
    创建新项目
    """
    try:
        project = await project_service.create_project(request)
        
        return ProjectResponse(
            success=True,
            project_id=project.id,
            message="项目创建成功"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"创建项目失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"创建项目失败: {str(e)}"
        )

@router.get("", response_model=ProjectListResponse)
async def get_projects():
    """
    获取项目列表
    """
    try:
        projects = await project_service.get_projects()
        
        return ProjectListResponse(
            projects=projects,
            total=len(projects)
        )
        
    except Exception as e:
        logger.error(f"获取项目列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取项目列表失败: {str(e)}"
        )

@router.get("/{project_id}", response_model=Project)
async def get_project(project_id: str):
    """
    获取单个项目信息
    """
    try:
        project = await project_service.get_project(project_id)
        
        if not project:
            raise HTTPException(
                status_code=404,
                detail="项目不存在"
            )
        
        return project
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目信息失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取项目信息失败: {str(e)}"
        )

@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(project_id: str, request: ProjectUpdateRequest):
    """
    更新项目信息
    """
    try:
        project = await project_service.update_project(project_id, request)
        
        if not project:
            raise HTTPException(
                status_code=404,
                detail="项目不存在"
            )
        
        return ProjectResponse(
            success=True,
            project_id=project.id,
            message="项目更新成功"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新项目失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"更新项目失败: {str(e)}"
        )

@router.delete("/{project_id}")
async def delete_project(project_id: str):
    """
    删除项目
    """
    try:
        success = await project_service.delete_project(project_id)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail="项目不存在"
            )
        
        return JSONResponse(
            content={"message": "项目删除成功"},
            status_code=200
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除项目失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"删除项目失败: {str(e)}"
        )

@router.get("/{project_id}/stats")
async def get_project_stats(project_id: str):
    """
    获取项目统计信息
    """
    try:
        project = await project_service.get_project(project_id)
        
        if not project:
            raise HTTPException(
                status_code=404,
                detail="项目不存在"
            )
        
        # TODO: 这里可以添加更多统计信息，如向量化状态、存储空间等
        stats = {
            "project_id": project.id,
            "project_name": project.name,
            "document_count": project.document_count,
            "created_at": project.created_at,
            "updated_at": project.updated_at
        }
        
        return JSONResponse(
            content=stats,
            status_code=200
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取项目统计失败: {str(e)}"
        )