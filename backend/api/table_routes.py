#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表格处理API路由
"""

import os
from fastapi import APIRouter, UploadFile, File, HTTPException, Query, Form
from fastapi.responses import FileResponse
from typing import List, Optional, Any
from pydantic import BaseModel
from services.table_service import TableService
from loguru import logger
import tempfile
import aiofiles
from pathlib import Path

router = APIRouter(prefix="/api/table", tags=["table"])
table_service = TableService()

class TableProcessRequest(BaseModel):
    """表格处理请求模型"""
    table_index: int = 0
    new_columns: Optional[List[str]] = ["dp_out", "dp_reason"]
    enable_evaluation: bool = True  # 是否启用智能评估功能

class TablePreviewResponse(BaseModel):
    """表格预览响应模型"""
    success: bool
    preview_data: Optional[List[List[str]]] = None
    total_rows: Optional[int] = None
    total_columns: Optional[int] = None
    total_tables: Optional[int] = None
    error: Optional[str] = None

class TableProcessResponse(BaseModel):
    """表格处理响应模型"""
    success: bool
    message: Optional[str] = None
    output_path: Optional[str] = None
    output_filename: Optional[str] = None
    tables_count: Optional[int] = None
    tables: Optional[List[List[Any]]] = None
    evaluation_enabled: Optional[bool] = None  # 是否启用了智能评估
    error: Optional[str] = None

@router.post("/upload-and-preview")
async def upload_and_preview_table(
    file: UploadFile = File(...),
    table_index: int = Query(0, description="表格索引，默认为0（第一个表格）"),
    max_rows: int = Query(10, description="预览最大行数")
) -> TablePreviewResponse:
    """
    上传Word文档并预览表格内容
    """
    try:
        logger.info(f"接收到文件上传请求: {file.filename}")
        
        # 验证文件类型
        if not file.filename.endswith(('.docx', '.doc')):
            raise HTTPException(status_code=400, detail="只支持Word文档格式(.docx, .doc)")
        
        # 保存临时文件
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, f"temp_{file.filename}")
        
        async with aiofiles.open(temp_file_path, 'wb') as temp_file:
            content = await file.read()
            await temp_file.write(content)
        
        logger.info(f"文件已保存到临时路径: {temp_file_path}")
        
        # 获取表格预览
        result = table_service.get_table_preview(
            temp_file_path, 
            table_index=table_index, 
            max_rows=max_rows
        )
        
        # 清理临时文件
        try:
            os.remove(temp_file_path)
        except:
            pass
        
        return TablePreviewResponse(**result)
        
    except Exception as e:
        logger.error(f"预览表格时出错: {e}")
        raise HTTPException(status_code=500, detail=f"预览表格失败: {str(e)}")

@router.post("/process")
async def process_table(
    file: UploadFile = File(...),
    table_index: int = Form(0),
    new_columns: str = Form('["dp_out", "dp_reason"]'),
    enable_evaluation: bool = Form(True),
    project_id: str = Form(None),
    fill_existing_columns: bool = Form(False)
) -> TableProcessResponse:
    """
    处理Word文档表格，添加新列并生成docx文件
    支持智能评估功能
    """
    try:
        logger.info(f"接收到表格处理请求: {file.filename}")
        
        # 验证文件类型
        if not file.filename.endswith(('.docx', '.doc')):
            raise HTTPException(status_code=400, detail="只支持Word文档格式(.docx, .doc)")
        
        # 解析new_columns参数
        try:
            import json
            parsed_columns = json.loads(new_columns)
        except:
            parsed_columns = ["dp_out", "dp_reason"]
        
        logger.info(f"智能评估模式: {enable_evaluation}, 项目ID: {project_id}, 填充现有列: {fill_existing_columns}")
        
        # 保存临时文件
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, f"temp_{file.filename}")
        
        async with aiofiles.open(temp_file_path, 'wb') as temp_file:
            content = await file.read()
            await temp_file.write(content)
        
        logger.info(f"文件已保存到临时路径: {temp_file_path}")
        
        # 处理表格（支持智能评估和项目范围）
        result = await table_service.process_document_table(
            temp_file_path,
            output_dir=temp_dir,
            new_columns=parsed_columns,
            enable_evaluation=enable_evaluation,
            project_id=project_id,
            fill_existing_columns=fill_existing_columns
        )
        
        # 清理原始临时文件
        try:
            os.remove(temp_file_path)
        except:
            pass
        
        return TableProcessResponse(**result)
        
    except Exception as e:
        logger.error(f"处理表格时出错: {e}")
        raise HTTPException(status_code=500, detail=f"处理表格失败: {str(e)}")

@router.get("/download/{filename}")
async def download_docx(filename: str):
    """
    下载生成的docx文件
    """
    try:
        logger.info(f"接收到文件下载请求: {filename}")
        
        # 构建文件路径
        temp_dir = tempfile.gettempdir()
        file_path = os.path.join(temp_dir, filename)
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        # 返回文件
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        
    except Exception as e:
        logger.error(f"下载文件时出错: {e}")
        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")

@router.get("/process-specific-file")
async def process_specific_file(
    file_path: str = Query(..., description="要处理的文件路径"),
    new_columns: List[str] = Query(["dp_out", "dp_reason"], description="新增列名"),
    enable_evaluation: bool = Query(True, description="是否启用智能评估功能")
) -> TableProcessResponse:
    """
    处理指定路径的Word文档表格
    支持智能评估功能
    """
    try:
        logger.info(f"处理指定文件: {file_path}，智能评估: {enable_evaluation}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        # 处理表格（支持智能评估）
        result = await table_service.process_document_table(
            file_path,
            output_dir=tempfile.gettempdir(),
            new_columns=new_columns,
            enable_evaluation=enable_evaluation
        )
        
        return TableProcessResponse(**result)
        
    except Exception as e:
        logger.error(f"处理指定文件时出错: {e}")
        raise HTTPException(status_code=500, detail=f"处理文件失败: {str(e)}")

@router.get("/preview-specific-file")
async def preview_specific_file(
    file_path: str = Query(..., description="要预览的文件路径"),
    table_index: int = Query(0, description="表格索引"),
    max_rows: int = Query(10, description="预览最大行数")
) -> TablePreviewResponse:
    """
    预览指定路径的Word文档表格
    """
    try:
        logger.info(f"预览指定文件: {file_path}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        # 获取表格预览
        result = table_service.get_table_preview(
            file_path,
            table_index=table_index,
            max_rows=max_rows
        )
        
        return TablePreviewResponse(**result)
        
    except Exception as e:
        logger.error(f"预览指定文件时出错: {e}")
        raise HTTPException(status_code=500, detail=f"预览文件失败: {str(e)}")