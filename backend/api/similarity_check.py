from fastapi import APIRouter, HTTPException, Query, UploadFile, File
from fastapi.responses import JSONResponse
from loguru import logger
from typing import Optional, Dict, Any
import tempfile
import os
import uuid

from services.similarity_check_service import similarity_check_service
from services.project_service import project_service
from models.schemas import *

router = APIRouter(prefix="/api/similarity_check", tags=["similarity_check"])

@router.get("/")
async def similarity_check(
    facet: str = Query(..., description="检索维度: project_background, product_requirements, core_features, user_segments, 或 all"),
    q: str = Query(..., description="查询文本"),
    top_k: int = Query(10, description="返回结果数量", ge=1, le=50),
    project_id: Optional[str] = Query(None, description="项目ID，限定检索范围")
) -> JSONResponse:
    """
    相似度检验接口
    
    根据指定的facet维度和查询文本，在向量数据库中检索相似文档段落，
    并通过大模型生成人工可核对的对比结果。
    
    Args:
        facet: 检索维度，支持的值：
            - project_background: 项目背景
            - product_requirements: 产品需求  
            - core_features: 产品核心功能
            - user_segments: 用户群体
            - all: 所有维度
        q: 查询文本
        top_k: 返回的结果数量，默认10个，最多50个
    
    Returns:
        JSON响应，包含：
        - candidates: 检索候选列表（带打分和片段）
        - ai_analysis: 大模型输出的对比JSON
        - total_found: 总共找到的文档数量
        - facet: 使用的检索维度
        - query: 原始查询文本
    """
    try:
        # 参数验证
        valid_facets = ['project_background', 'product_requirements', 'core_features', 'user_segments', 'all']
        if facet not in valid_facets:
            raise HTTPException(
                status_code=400,
                detail=f"无效的facet参数。支持的值: {', '.join(valid_facets)}"
            )
        
        if not q or len(q.strip()) == 0:
            raise HTTPException(
                status_code=400,
                detail="查询文本不能为空"
            )
        
        if len(q) > 1000:
            raise HTTPException(
                status_code=400,
                detail="查询文本长度不能超过1000字符"
            )
        
        # 验证项目ID
        if project_id:
            project = await project_service.get_project(project_id)
            if not project:
                raise HTTPException(
                    status_code=400,
                    detail="指定的项目不存在"
                )
        
        logger.info(f"收到相似度检验请求: facet={facet}, query={q[:50]}..., top_k={top_k}, project_id={project_id}")
        
        # 初始化服务（如果尚未初始化）
        if not hasattr(similarity_check_service, 'collection') or not similarity_check_service.collection:
            init_success = await similarity_check_service.initialize()
            if not init_success:
                raise HTTPException(
                    status_code=503,
                    detail="相似度检验服务初始化失败，请稍后重试"
                )
        
        # 执行相似度检索
        result = await similarity_check_service.similarity_search(
            facet=facet,
            user_query=q.strip(),
            top_k=top_k,
            project_id=project_id
        )
        
        # 构建响应数据
        response_data = {
            "success": True,
            "message": "相似度检验完成",
            "data": {
                "candidates": result['candidates'],
                "ai_analysis": result['ai_analysis'],
                "total_found": result['total_found'],
                "facet": result['facet'],
                "query": result['query'],
                "returned_count": len(result['candidates'])
            }
        }
        
        logger.info(f"相似度检验完成，返回 {len(result['candidates'])} 个结果")
        
        return JSONResponse(
            content=response_data,
            status_code=200
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"相似度检验失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"相似度检验失败: {str(e)}"
        )

@router.post("/upload_and_process")
async def upload_and_process_document(
    file: UploadFile = File(..., description="要处理的文档文件"),
    project_id: Optional[str] = Query(None, description="项目ID")
) -> JSONResponse:
    """
    上传并处理文档，将其切分、向量化并存储到知识库
    
    Args:
        file: 上传的文档文件，支持 .docx 和 .pdf 格式
    
    Returns:
        JSON响应，包含处理结果信息
    """
    try:
        # 验证文件
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 检查文件格式
        allowed_extensions = ['.docx', '.doc', '.pdf']
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件格式。支持的格式: {', '.join(allowed_extensions)}"
            )
        
        # 检查文件大小 (20MB限制)
        file_content = await file.read()
        if len(file_content) > 20 * 1024 * 1024:  # 20MB
            raise HTTPException(status_code=400, detail="文件大小不能超过20MB")
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name
        
        try:
            # 验证项目ID
            if project_id:
                project = await project_service.get_project(project_id)
                if not project:
                    raise HTTPException(
                        status_code=400,
                        detail="指定的项目不存在"
                    )
            
            # 生成文档ID
            doc_id = f"doc_{uuid.uuid4().hex[:8]}"
            
            # 初始化服务
            if not hasattr(similarity_check_service, 'collection') or not similarity_check_service.collection:
                init_success = await similarity_check_service.initialize()
                if not init_success:
                    raise HTTPException(
                        status_code=503,
                        detail="相似度检验服务初始化失败，请稍后重试"
                    )
            
            logger.info(f"开始处理上传的文档: {file.filename}")
            
            # 处理并存储文档
            result = await similarity_check_service.process_and_store_document(
                doc_id=doc_id,
                file_path=temp_file_path,
                doc_title=file.filename,
                project_id=project_id
            )
            
            response_data = {
                "success": True,
                "message": "文档处理完成",
                "data": result
            }
            
            logger.info(f"文档 {file.filename} 处理完成")
            
            return JSONResponse(
                content=response_data,
                status_code=200
            )
            
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文档处理失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"文档处理失败: {str(e)}"
        )

@router.get("/facets")
async def get_available_facets() -> JSONResponse:
    """
    获取可用的facet维度列表
    
    Returns:
        JSON响应，包含所有可用的facet维度及其描述
    """
    try:
        facets_info = {
            "project_background": {
                "name": "项目背景",
                "description": "项目起因、行业背景、现状问题、目标与范围",
                "keywords": ["背景", "现状", "目标", "痛点", "起因", "环境", "前提", "问题", "挑战"]
            },
            "product_requirements": {
                "name": "产品需求",
                "description": "产品需求、功能要求、约束条件、验收标准",
                "keywords": ["需求", "约束", "PRD", "边界", "验收", "要求", "规范", "标准", "条件"]
            },
            "core_features": {
                "name": "产品核心功能",
                "description": "核心功能、主要模块、技术能力、系统特性",
                "keywords": ["功能", "模块", "接口", "流程", "能力", "特性", "组件", "服务", "系统"]
            },
            "user_segments": {
                "name": "用户群体",
                "description": "目标用户、用户画像、使用场景、行业领域",
                "keywords": ["用户", "画像", "角色", "行业", "场景", "客户", "群体", "对象", "受众"]
            },
            "all": {
                "name": "所有维度",
                "description": "不限制维度，搜索所有内容",
                "keywords": []
            }
        }
        
        response_data = {
            "success": True,
            "message": "获取facet信息成功",
            "data": {
                "facets": facets_info,
                "total_count": len(facets_info)
            }
        }
        
        return JSONResponse(
            content=response_data,
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"获取facet信息失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取facet信息失败: {str(e)}"
        )

@router.get("/stats")
async def get_knowledge_base_stats() -> JSONResponse:
    """
    获取知识库统计信息
    
    Returns:
        JSON响应，包含知识库的统计信息
    """
    try:
        # 初始化服务
        if not hasattr(similarity_check_service, 'collection') or not similarity_check_service.collection:
            init_success = await similarity_check_service.initialize()
            if not init_success:
                raise HTTPException(
                    status_code=503,
                    detail="相似度检验服务初始化失败，请稍后重试"
                )
        
        # 获取集合统计信息
        if similarity_check_service.collection:
            total_count = similarity_check_service.collection.count()
            
            # 获取一些示例数据来分析facet分布
            sample_results = similarity_check_service.collection.query(
                query_embeddings=[[0.0] * 1024],  # 虚拟查询向量
                n_results=min(100, total_count),
                include=["metadatas"]
            )
            
            # 统计facet分布
            facet_stats = {}
            doc_stats = {}
            
            if sample_results and sample_results["metadatas"]:
                for metadata in sample_results["metadatas"][0]:
                    # 统计facet分布
                    facets = metadata.get('facet_candidates', [])
                    for facet in facets:
                        facet_stats[facet] = facet_stats.get(facet, 0) + 1
                    
                    # 统计文档分布
                    doc_title = metadata.get('doc_title', 'unknown')
                    doc_stats[doc_title] = doc_stats.get(doc_title, 0) + 1
            
            stats = {
                "total_chunks": total_count,
                "facet_distribution": facet_stats,
                "document_distribution": doc_stats,
                "collection_name": similarity_check_service.collection_name
            }
        else:
            stats = {
                "total_chunks": 0,
                "facet_distribution": {},
                "document_distribution": {},
                "collection_name": similarity_check_service.collection_name,
                "status": "collection_not_available"
            }
        
        response_data = {
            "success": True,
            "message": "获取知识库统计信息成功",
            "data": stats
        }
        
        return JSONResponse(
            content=response_data,
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"获取知识库统计信息失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取知识库统计信息失败: {str(e)}"
        )

@router.delete("/clear")
async def clear_knowledge_base() -> JSONResponse:
    """
    清空知识库（谨慎使用）
    
    Returns:
        JSON响应，包含清空操作的结果
    """
    try:
        # 初始化服务
        if not hasattr(similarity_check_service, 'collection') or not similarity_check_service.collection:
            init_success = await similarity_check_service.initialize()
            if not init_success:
                raise HTTPException(
                    status_code=503,
                    detail="相似度检验服务初始化失败，请稍后重试"
                )
        
        # 获取清空前的统计信息
        before_count = 0
        if similarity_check_service.collection:
            before_count = similarity_check_service.collection.count()
        
        # 删除并重新创建集合
        if similarity_check_service.chroma_client.client:
            try:
                similarity_check_service.chroma_client.client.delete_collection(
                    name=similarity_check_service.collection_name
                )
                logger.info(f"已删除集合: {similarity_check_service.collection_name}")
            except Exception as e:
                logger.warning(f"删除集合失败（可能不存在）: {e}")
            
            # 重新创建集合
            similarity_check_service.collection = similarity_check_service.chroma_client.client.create_collection(
                name=similarity_check_service.collection_name,
                metadata={"hnsw:space": "cosine"}
            )
            logger.info(f"已重新创建集合: {similarity_check_service.collection_name}")
        
        response_data = {
            "success": True,
            "message": "知识库清空成功",
            "data": {
                "cleared_count": before_count,
                "collection_name": similarity_check_service.collection_name
            }
        }
        
        logger.info(f"知识库清空完成，清除了 {before_count} 个文档块")
        
        return JSONResponse(
            content=response_data,
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"清空知识库失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"清空知识库失败: {str(e)}"
        )