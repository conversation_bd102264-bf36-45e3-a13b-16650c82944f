from fastapi import APIRouter, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
from loguru import logger
import tempfile
import os
from typing import Dict, Any

from services.duplicate_detection_service import duplicate_detection_service
from models.schemas import DuplicateDetectionResult

router = APIRouter(prefix="/api/similarity", tags=["similarity"])

@router.post("/check")
async def check_similarity(file: UploadFile = File(...)):
    """
    检查上传文档与已存储文档的相似度
    """
    try:
        # 验证文件类型
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 检查文件格式
        allowed_extensions = ['.docx', '.doc', '.pdf']
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件格式。支持的格式: {', '.join(allowed_extensions)}"
            )
        
        # 检查文件大小 (10MB限制)
        file_content = await file.read()
        if len(file_content) > 10 * 1024 * 1024:  # 10MB
            raise HTTPException(status_code=400, detail="文件大小不能超过10MB")
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name
        
        try:
            # 生成临时文档ID
            import uuid
            temp_document_id = f"temp_{uuid.uuid4().hex[:8]}"
            
            # 执行相似度检测
            logger.info(f"开始检测文档 {file.filename} 的相似度")
            detection_result = await duplicate_detection_service.check_document_duplicate(
                document_id=temp_document_id,
                file_path=temp_file_path,
                document_name=file.filename
            )
            
            # 构建响应数据
            response_data = {
                "success": True,
                "message": "相似度检测完成",
                "data": {
                    "is_duplicate": detection_result.is_duplicate,
                    "similarity_score": detection_result.similarity_score,
                    "duplicate_document_name": detection_result.duplicate_document_name,
                    "similar_content": detection_result.similar_content,
                    "detection_time": detection_result.detection_time.isoformat() if detection_result.detection_time else None,
                    "ai_analysis": detection_result.ai_analysis  # 添加AI分析结果
                }
            }
            
            logger.info(f"文档 {file.filename} 相似度检测完成，相似度: {detection_result.similarity_score:.2%}")
            
            return JSONResponse(
                content=response_data,
                status_code=200
            )
            
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"相似度检测失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"相似度检测失败: {str(e)}"
        )

@router.post("/ai-analysis")
async def generate_ai_analysis(file: UploadFile = File(...)):
    """
    单独生成AI智能分析
    """
    try:
        # 验证文件类型
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 检查文件格式
        allowed_extensions = ['.docx', '.doc', '.pdf']
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件格式。支持的格式: {', '.join(allowed_extensions)}"
            )
        
        # 检查文件大小 (10MB限制)
        file_content = await file.read()
        if len(file_content) > 10 * 1024 * 1024:  # 10MB
            raise HTTPException(status_code=400, detail="文件大小不能超过10MB")
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name
        
        try:
            # 生成临时文档ID
            import uuid
            temp_document_id = f"temp_{uuid.uuid4().hex[:8]}"
            
            # 执行AI分析
            logger.info(f"开始为文档 {file.filename} 生成AI分析")
            ai_analysis_result = await duplicate_detection_service.ai_duplicate_analysis(
                document_id=temp_document_id,
                file_path=temp_file_path,
                document_name=file.filename
            )
            
            # 构建响应数据
            response_data = {
                "success": True,
                "message": "AI智能分析完成",
                "data": {
                    "ai_analysis": ai_analysis_result
                }
            }
            
            logger.info(f"文档 {file.filename} AI分析完成")
            
            return JSONResponse(
                content=response_data,
                status_code=200
            )
            
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI分析失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"AI分析失败: {str(e)}"
        )

@router.get("/debug/stored-documents")
async def get_stored_documents_debug():
    """
    获取当前存储的文档信息（调试用）
    """
    try:
        stored_info = duplicate_detection_service.get_stored_documents_info()
        
        return JSONResponse(
            content={
                "success": True,
                "message": f"当前存储了 {stored_info['total_count']} 个文档用于相似度比较",
                "data": stored_info
            },
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"获取存储文档信息失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取存储文档信息失败: {str(e)}"
        )