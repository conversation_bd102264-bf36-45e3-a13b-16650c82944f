from fastapi import APIRouter, UploadFile, File, HTTPException, Query
from fastapi.responses import JSONResponse
from typing import Optional
from loguru import logger
from models.schemas import (
    DocumentUploadResponse, DocumentListResponse, VectorizeResponse, ErrorResponse
)
from services.document_service import document_service
from services.project_service import project_service
from services.chroma_client import chroma_client
import asyncio

router = APIRouter(prefix="/api/documents", tags=["documents"])

@router.post("/upload", response_model=DocumentUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    project_id: Optional[str] = Query(None, description="项目ID")
):
    """
    上传DOCX文档
    """
    try:
        # 检查文件类型
        if not file.filename or not file.filename.lower().endswith('.docx'):
            raise HTTPException(
                status_code=400, 
                detail="只支持DOCX格式文件"
            )
        
        # 读取文件内容
        file_content = await file.read()
        
        if not file_content:
            raise HTTPException(
                status_code=400,
                detail="文件内容为空"
            )
        
        # 验证项目ID
        if project_id:
            project = await project_service.get_project(project_id)
            if not project:
                raise HTTPException(
                    status_code=400,
                    detail="指定的项目不存在"
                )
        else:
            # 使用默认项目
            project_id = project_service.get_default_project_id()
        
        # 上传文档
        document = await document_service.upload_document(
            file_content=file_content,
            filename=file.filename,
            project_id=project_id
        )
        
        # 更新项目文档数量
        await project_service.update_document_count(project_id, 1)
        
        return DocumentUploadResponse(
            success=True,
            document_id=document.id,
            message="文档上传成功",
            filename=document.original_name
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文档上传失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"文档上传失败: {str(e)}"
        )

@router.get("", response_model=DocumentListResponse)
async def get_documents(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    project_id: Optional[str] = Query(None, description="项目ID，不指定则返回所有项目的文档")
):
    """
    获取文档列表
    """
    try:
        result = await document_service.get_documents(
            page=page, 
            page_size=page_size,
            project_id=project_id
        )
        
        return DocumentListResponse(
            documents=result["documents"],
            total=result["total"],
            page=result["page"],
            page_size=result["page_size"]
        )
        
    except Exception as e:
        logger.error(f"获取文档列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取文档列表失败: {str(e)}"
        )

@router.post("/{document_id}/vectorize", response_model=VectorizeResponse)
async def vectorize_document(document_id: str):
    """
    对文档进行向量化处理
    """
    try:
        # 异步执行向量化任务
        result = await document_service.vectorize_document(document_id)
        
        return VectorizeResponse(
            success=result["success"],
            status=result["status"],
            task_id=result["task_id"],
            chunks_count=result["chunks_count"]
        )
        
    except Exception as e:
        logger.error(f"文档向量化失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"文档向量化失败: {str(e)}"
        )

@router.delete("/{document_id}")
async def delete_document(document_id: str):
    """
    删除文档
    """
    try:
        # 获取文档信息以更新项目文档数量
        document = await document_service.get_document(document_id)
        if not document:
            raise HTTPException(
                status_code=404,
                detail="文档不存在"
            )
        
        success = await document_service.delete_document(document_id)
        
        if success:
            # 更新项目文档数量
            if document.project_id:
                await project_service.update_document_count(document.project_id, -1)
            
            return JSONResponse(
                content={"message": "文档删除成功"},
                status_code=200
            )
        else:
            raise HTTPException(
                status_code=404,
                detail="文档不存在"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文档失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"删除文档失败: {str(e)}"
        )

@router.get("/{document_id}")
async def get_document(document_id: str):
    """
    获取单个文档信息
    """
    try:
        if document_id not in document_service.documents_db:
            raise HTTPException(
                status_code=404,
                detail="文档不存在"
            )
        
        document = document_service.documents_db[document_id]
        return document
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档信息失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取文档信息失败: {str(e)}"
        )

@router.get("/{document_id}/chunks")
async def get_document_chunks(document_id: str):
    """
    获取文档的所有分块信息
    """
    try:
        # 检查文档是否存在
        if document_id not in document_service.documents_db:
            raise HTTPException(
                status_code=404,
                detail="文档不存在"
            )
        
        document = document_service.documents_db[document_id]
        
        # 检查文档是否已向量化
        if document.status.value != "completed":
            raise HTTPException(
                status_code=400,
                detail="文档尚未完成向量化，无法查看分块信息"
            )
        
        # 从ChromaDB获取分块信息
        chunks = await chroma_client.get_document_chunks(document_id)
        
        return JSONResponse(
            content={
                "success": True,
                "document_id": document_id,
                "document_name": document.original_name,
                "total_chunks": len(chunks),
                "chunks": chunks
            },
            status_code=200
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档分块失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取文档分块失败: {str(e)}"
        )