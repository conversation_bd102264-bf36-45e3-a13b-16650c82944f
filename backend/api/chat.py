from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse
from typing import Optional
from loguru import logger
from models.schemas import (
    ChatRequest, ChatResponse, ChatHistoryResponse, ErrorResponse
)
from services.chat_service import chat_service
from services.project_service import project_service

router = APIRouter(prefix="/api/chat", tags=["chat"])

@router.post("/ask", response_model=ChatResponse)
async def ask_question(request: ChatRequest):
    """
    提问接口
    """
    try:
        # 验证问题长度
        if not request.question or len(request.question.strip()) == 0:
            raise HTTPException(
                status_code=400,
                detail="问题不能为空"
            )
        
        if len(request.question) > 1000:
            raise HTTPException(
                status_code=400,
                detail="问题长度不能超过1000字符"
            )
        
        # 验证项目ID
        if request.project_id:
            project = await project_service.get_project(request.project_id)
            if not project:
                raise HTTPException(
                    status_code=400,
                    detail="指定的项目不存在"
                )
        
        # 处理问题
        response = await chat_service.process_question(request)
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"问答处理失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"问答处理失败: {str(e)}"
        )

@router.get("/history/{session_id}", response_model=ChatHistoryResponse)
async def get_chat_history(
    session_id: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=100, description="每页数量")
):
    """
    获取聊天历史
    """
    try:
        history = await chat_service.get_chat_history(
            session_id=session_id,
            page=page,
            page_size=page_size
        )
        
        return history
        
    except Exception as e:
        logger.error(f"获取聊天历史失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取聊天历史失败: {str(e)}"
        )

@router.get("/sessions")
async def get_chat_sessions():
    """
    获取所有聊天会话
    """
    try:
        sessions = await chat_service.get_sessions()
        
        return {
            "sessions": sessions,
            "total": len(sessions)
        }
        
    except Exception as e:
        logger.error(f"获取会话列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取会话列表失败: {str(e)}"
        )

@router.delete("/sessions/{session_id}")
async def delete_chat_session(session_id: str):
    """
    删除聊天会话
    """
    try:
        success = await chat_service.delete_session(session_id)
        
        if success:
            return JSONResponse(
                content={"message": "会话删除成功"},
                status_code=200
            )
        else:
            raise HTTPException(
                status_code=404,
                detail="会话不存在"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除会话失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"删除会话失败: {str(e)}"
        )

@router.post("/sessions/clear")
async def clear_all_sessions():
    """
    清空所有会话（用于测试）
    """
    try:
        success = await chat_service.clear_all_sessions()
        
        if success:
            return JSONResponse(
                content={"message": "所有会话已清空"},
                status_code=200
            )
        else:
            raise HTTPException(
                status_code=500,
                detail="清空会话失败"
            )
            
    except Exception as e:
        logger.error(f"清空会话失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"清空会话失败: {str(e)}"
        )

@router.get("/test")
async def test_chat_api():
    """
    测试聊天API
    """
    return {
        "message": "聊天API正常工作",
        "timestamp": "2024-01-01T00:00:00Z",
        "status": "ok"
    }