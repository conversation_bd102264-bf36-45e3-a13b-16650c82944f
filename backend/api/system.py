from fastapi import APIRouter, HTTPException
from loguru import logger
from datetime import datetime
from models.schemas import SystemStatus
from services.alibaba_client import alibaba_client
from services.chroma_client import chroma_client
from services.document_service import document_service
import time

router = APIRouter(prefix="/api/system", tags=["system"])

# 记录系统启动时间
start_time = time.time()

@router.get("/status", response_model=SystemStatus)
async def get_system_status():
    """
    获取系统状态
    """
    try:
        # 测试ChromaDB连接
        chromadb_connected = await chroma_client.test_connection()
        
        # 测试阿里云API连接
        alibaba_api_connected = await alibaba_client.test_connection()
        
        # 获取文档统计
        documents_count = len(document_service.documents_db)
        
        # 获取向量统计
        chroma_stats = await chroma_client.get_collection_stats()
        vectors_count = chroma_stats.get("total_vectors", 0)
        
        # 计算运行时间
        uptime_seconds = int(time.time() - start_time)
        uptime = format_uptime(uptime_seconds)
        
        # 确定整体状态
        if chromadb_connected and alibaba_api_connected:
            status = "healthy"
        elif chromadb_connected or alibaba_api_connected:
            status = "partial"
        else:
            status = "unhealthy"
        
        return SystemStatus(
            status=status,
            chromadb_connected=chromadb_connected,
            alibaba_api_connected=alibaba_api_connected,
            documents_count=documents_count,
            vectors_count=vectors_count,
            uptime=uptime
        )
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return SystemStatus(
            status="error",
            chromadb_connected=False,
            alibaba_api_connected=False,
            documents_count=0,
            vectors_count=0,
            uptime="unknown"
        )

@router.get("/health")
async def health_check():
    """
    健康检查接口
    """
    try:
        # 简单的健康检查
        return {
            "status": "ok",
            "timestamp": datetime.now().isoformat(),
            "service": "doc-qa-system",
            "version": "1.0.0"
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(
            status_code=500,
            detail="服务不健康"
        )

@router.get("/info")
async def get_system_info():
    """
    获取系统信息
    """
    try:
        return {
            "service_name": "文档向量化问答系统",
            "version": "1.0.0",
            "description": "基于FastAPI和ChromaDB的文档问答系统",
            "features": [
                "DOCX文档上传",
                "文档向量化",
                "智能问答",
                "聊天历史",
                "文档管理"
            ],
            "apis": {
                "documents": "/api/documents",
                "chat": "/api/chat",
                "system": "/api/system"
            },
            "external_services": {
                "embedding_model": "text-embedding-v4",
                "chat_model": "qwen-plus",
                "vector_database": "ChromaDB"
            }
        }
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取系统信息失败: {str(e)}"
        )

@router.post("/test/connections")
async def test_all_connections():
    """
    测试所有外部连接
    """
    try:
        results = {}
        
        # 测试ChromaDB
        try:
            chromadb_ok = await chroma_client.test_connection()
            results["chromadb"] = {
                "status": "connected" if chromadb_ok else "disconnected",
                "details": await chroma_client.get_collection_stats()
            }
        except Exception as e:
            results["chromadb"] = {
                "status": "error",
                "error": str(e)
            }
        
        # 测试阿里云API
        try:
            alibaba_ok = await alibaba_client.test_connection()
            results["alibaba_api"] = {
                "status": "connected" if alibaba_ok else "disconnected",
                "embedding_model": "text-embedding-v4",
                "chat_model": "qwen-plus"
            }
        except Exception as e:
            results["alibaba_api"] = {
                "status": "error",
                "error": str(e)
            }
        
        return {
            "test_time": datetime.now().isoformat(),
            "results": results
        }
        
    except Exception as e:
        logger.error(f"连接测试失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"连接测试失败: {str(e)}"
        )

def format_uptime(seconds: int) -> str:
    """
    格式化运行时间
    """
    days = seconds // 86400
    hours = (seconds % 86400) // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    
    if days > 0:
        return f"{days}天 {hours}小时 {minutes}分钟"
    elif hours > 0:
        return f"{hours}小时 {minutes}分钟"
    elif minutes > 0:
        return f"{minutes}分钟 {seconds}秒"
    else:
        return f"{seconds}秒"