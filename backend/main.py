from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from contextlib import asynccontextmanager
from loguru import logger
import sys
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config import settings
from api import documents, chat, system, table_routes, similarity, similarity_check, projects
from services.chroma_client import chroma_client
from services.alibaba_client import alibaba_client
from services.document_service import document_service
from services.similarity_check_service import similarity_check_service
from services.database_service import database_service

# 配置日志
logger.remove()
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level=settings.log_level
)
logger.add(
    "logs/app.log",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level=settings.log_level,
    rotation="1 day",
    retention="30 days"
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理
    """
    # 启动时执行
    logger.info("🚀 文档向量化问答系统启动中...")
    
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)
    
    # 测试外部服务连接
    try:
        # 初始化数据库服务
        logger.info("🔄 初始化数据库服务...")
        await database_service.initialize()
        logger.info("✅ 数据库服务初始化成功")
        
        # 初始化ChromaDB客户端
        await chroma_client.initialize()
        
        # 测试ChromaDB连接
        chroma_ok = await chroma_client.test_connection()
        if chroma_ok:
            logger.info("✅ ChromaDB连接成功")
        else:
            logger.warning("⚠️ ChromaDB连接失败，将使用本地模式")
        
        # 测试阿里云API连接
        alibaba_ok = await alibaba_client.test_connection()
        if alibaba_ok:
            logger.info("✅ 阿里云百炼API连接成功")
        else:
            logger.warning("⚠️ 阿里云百炼API连接失败")
        
        # 初始化文档服务，从ChromaDB恢复文档信息
        logger.info("🔄 初始化文档服务...")
        await document_service.initialize()
        
        # 初始化相似度检验服务
        logger.info("🔄 初始化相似度检验服务...")
        similarity_init_ok = await similarity_check_service.initialize()
        if similarity_init_ok:
            logger.info("✅ 相似度检验服务初始化成功")
        else:
            logger.warning("⚠️ 相似度检验服务初始化失败")
            
    except Exception as e:
        logger.error(f"❌ 外部服务连接测试失败: {e}")
    
    logger.info("🎉 系统启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("👋 系统正在关闭...")
    
    # 关闭数据库连接
    await database_service.close()

# 创建FastAPI应用
app = FastAPI(
    title="文档向量化问答系统",
    description="基于FastAPI、ChromaDB和阿里云百炼的智能文档问答系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(projects.router)
app.include_router(documents.router)
app.include_router(chat.router)
app.include_router(system.router)
app.include_router(table_routes.router)
app.include_router(similarity.router)
app.include_router(similarity_check.router)

# 根路径
@app.get("/")
async def root():
    """
    根路径，返回系统信息
    """
    return {
        "message": "欢迎使用文档向量化问答系统",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc",
        "status": "running"
    }

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """
    全局异常处理器
    """
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "detail": "系统发生未知错误，请稍后重试"
        }
    )

# HTTP异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """
    HTTP异常处理器
    """
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code
        }
    )

if __name__ == "__main__":
    import uvicorn
    
    logger.info(f"启动服务器: {settings.app_host}:{settings.app_port}")
    
    uvicorn.run(
        "main:app",
        host=settings.app_host,
        port=settings.app_port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )