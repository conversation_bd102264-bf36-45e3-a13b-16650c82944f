#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库表创建脚本
用于创建所有必要的数据库表
"""

import asyncio
import sys
import os
from loguru import logger

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.database_service import database_service, Base
from models.database_models import (
    ProjectModel,
    DocumentModel, 
    ChatSessionModel,
    ChatMessageModel,
    SystemConfigModel,
    OperationLogModel
)

async def create_all_tables():
    """
    创建所有数据库表
    """
    try:
        logger.info("🚀 开始创建数据库表...")
        
        # 初始化数据库连接
        await database_service.initialize()
        
        # 获取异步引擎
        engine = database_service.async_engine
        if not engine:
            raise Exception("数据库引擎未初始化")
        
        # 创建所有表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("✅ 数据库表创建成功")
        
        # 验证表是否创建成功
        await verify_tables()
        
    except Exception as e:
        logger.error(f"❌ 创建数据库表失败: {e}")
        raise
    finally:
        await database_service.close()

async def verify_tables():
    """
    验证表是否创建成功
    """
    try:
        logger.info("🔍 验证数据库表...")
        
        expected_tables = [
            'projects',
            'documents', 
            'chat_sessions',
            'chat_messages',
            'system_config',
            'operation_logs'
        ]
        
        from sqlalchemy import text
        
        async with database_service.get_session() as session:
            for table_name in expected_tables:
                result = await session.execute(
                    text(f"SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '{table_name}')") 
                )
                exists = result.scalar()
                if exists:
                    logger.info(f"✅ 表 {table_name} 创建成功")
                else:
                    logger.error(f"❌ 表 {table_name} 创建失败")
        
        logger.info("🎉 数据库表验证完成")
        
    except Exception as e:
        logger.error(f"验证数据库表失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(create_all_tables())