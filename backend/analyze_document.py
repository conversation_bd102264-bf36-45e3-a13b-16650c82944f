#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档表格分析脚本
用于分析Word文档中的表格结构
"""

import sys
from docx import Document
from pathlib import Path

def analyze_word_document(file_path):
    """
    分析Word文档中的表格结构
    """
    try:
        # 加载文档
        doc = Document(file_path)
        print(f"正在分析文档: {file_path}")
        print(f"文档段落数: {len(doc.paragraphs)}")
        print(f"文档表格数: {len(doc.tables)}")
        print("\n" + "="*50)
        
        # 分析每个表格
        for i, table in enumerate(doc.tables):
            print(f"\n表格 {i+1}:")
            print(f"  行数: {len(table.rows)}")
            print(f"  列数: {len(table.columns) if table.rows else 0}")
            
            # 显示表格内容
            if table.rows:
                print("\n  表格内容:")
                for row_idx, row in enumerate(table.rows):
                    row_data = []
                    for cell in row.cells:
                        cell_text = cell.text.strip().replace('\n', ' ')
                        row_data.append(cell_text[:30] + '...' if len(cell_text) > 30 else cell_text)
                    print(f"    行 {row_idx+1}: {row_data}")
                    
                    # 只显示前10行，避免输出过长
                    if row_idx >= 9:
                        print(f"    ... (还有 {len(table.rows) - 10} 行)")
                        break
        
        return True
        
    except Exception as e:
        print(f"分析文档时出错: {e}")
        return False

if __name__ == "__main__":
    # 指定要分析的文档路径
    doc_path = "../数字化项目评审/附件2抚州市数字化项目自评表（2025版）.docx"
    
    if not Path(doc_path).exists():
        print(f"文档不存在: {doc_path}")
        sys.exit(1)
    
    analyze_word_document(doc_path)