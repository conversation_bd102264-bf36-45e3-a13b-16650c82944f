#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试指引性说明识别功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.table_service import TableService

def test_reference_explanation():
    """
    测试指引性说明识别功能
    """
    table_service = TableService()
    
    # 测试用例：应该被识别为指引性说明的内容
    reference_cases = [
        "见附件",
        "见法规",
        "见附件1",
        "详见附件2",
        "参见相关法规",
        "参考附件中的技术方案",
        "按照附件要求执行",
        "依据附件1中的标准",
        "遵照相关法规执行",
        "执行附件中的规定",
        "参照国家标准",
        "见通知要求",
        "附件",
        "法规",
        "标准"
    ]
    
    # 测试用例：不应该被识别为指引性说明的内容
    non_reference_cases = [
        "项目已完成技术方案设计，包含系统架构、功能模块等详细内容",
        "资金来源为财政拨款，已到位500万元",
        "今天天气很好，阳光明媚",
        "系统采用微服务架构，支持高并发访问",
        "项目团队具备丰富的开发经验",
        "数据库采用MySQL，支持事务处理",
        "用户界面友好，操作简单",
        "安全措施完善，数据加密存储"
    ]
    
    print("=== 测试指引性说明识别功能 ===")
    print()
    
    print("--- 测试指引性说明（应该返回True）---")
    for i, case in enumerate(reference_cases, 1):
        result = table_service._is_reference_explanation(case)
        status = "✅ 正确" if result else "❌ 错误"
        print(f"{i:2d}. {case:<30} -> {result} {status}")
    
    print()
    print("--- 测试非指引性说明（应该返回False）---")
    for i, case in enumerate(non_reference_cases, 1):
        result = table_service._is_reference_explanation(case)
        status = "✅ 正确" if not result else "❌ 错误"
        print(f"{i:2d}. {case[:30]:<30} -> {result} {status}")
    
    # 统计结果
    reference_correct = sum(1 for case in reference_cases if table_service._is_reference_explanation(case))
    non_reference_correct = sum(1 for case in non_reference_cases if not table_service._is_reference_explanation(case))
    
    total_cases = len(reference_cases) + len(non_reference_cases)
    correct_cases = reference_correct + non_reference_correct
    
    print()
    print("=== 测试结果统计 ===")
    print(f"指引性说明识别正确率: {reference_correct}/{len(reference_cases)} ({reference_correct/len(reference_cases)*100:.1f}%)")
    print(f"非指引性说明识别正确率: {non_reference_correct}/{len(non_reference_cases)} ({non_reference_correct/len(non_reference_cases)*100:.1f}%)")
    print(f"总体正确率: {correct_cases}/{total_cases} ({correct_cases/total_cases*100:.1f}%)")
    
    if correct_cases == total_cases:
        print("🎉 所有测试用例都通过了！")
    else:
        print(f"⚠️ 有 {total_cases - correct_cases} 个测试用例失败")

if __name__ == "__main__":
    test_reference_explanation()