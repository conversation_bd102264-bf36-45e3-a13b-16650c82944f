#!/bin/bash

# Frontend Docker build script with ARM64 compatibility
# This script handles the Rollup ARM64 musl issue

set -e

echo "🚀 Building frontend Docker image..."

# Check if we're on ARM64 (Apple Silicon)
ARCH=$(uname -m)
if [[ "$ARCH" == "arm64" ]]; then
    echo "📱 Detected ARM64 architecture (Apple Silicon)"
    echo "🔧 Building with platform specification to avoid Rollup ARM64 issues..."
    
    # Build with explicit platform specification
    docker build --platform linux/amd64 -t doc-qa-frontend:latest ./frontend
else
    echo "💻 Detected x86_64 architecture"
    echo "🔧 Building normally..."
    
    # Build normally
    docker build -t doc-qa-frontend:latest ./frontend
fi

echo "✅ Frontend Docker image built successfully!"

# Optional: Show image info
echo "📊 Image information:"
docker images doc-qa-frontend:latest
