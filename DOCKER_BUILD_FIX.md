# Docker Build Fix for ARM64 Rollup Issue

## Problem
The Docker build fails with the error:
```
Error: Cannot find module @rollup/rollup-linux-arm64-musl
```

This is a known issue when building Node.js applications with Vite/Rollup on ARM64 architecture (Apple Silicon Macs).

## Solutions

### Solution 1: Use Platform-Specific Build (Recommended)

The Dockerfile has been updated to specify the platform explicitly:

```dockerfile
FROM --platform=linux/amd64 dockerpull.cn/node:18-alpine AS builder
```

**Build using the provided script:**
```bash
./build-frontend.sh
```

**Or build manually:**
```bash
# For ARM64 (Apple Silicon)
docker build --platform linux/amd64 -t doc-qa-frontend:latest ./frontend

# For x86_64
docker build -t doc-qa-frontend:latest ./frontend
```

### Solution 2: Clean Build Process

The Dockerfile now uses a cleaner build process:
1. Clears npm cache
2. Removes existing node_modules and package-lock.json
3. Fresh npm install
4. Build and prune

### Solution 3: Alternative Local Build

If Docker build still fails, you can build locally and copy:

```bash
cd frontend
rm -rf node_modules package-lock.json
npm install
npm run build
```

Then use a simpler Dockerfile that just copies the built files.

## Verification

After building, verify the image:
```bash
docker images doc-qa-frontend:latest
docker run --rm -p 8080:80 doc-qa-frontend:latest
```

## Additional Notes

- The `--platform=linux/amd64` flag forces the build to use x86_64 architecture
- This may be slightly slower on ARM64 machines but ensures compatibility
- The build script automatically detects your architecture and applies the appropriate build method

## Troubleshooting

If you still encounter issues:

1. **Clear Docker cache:**
   ```bash
   docker system prune -a
   ```

2. **Update Docker Desktop** to the latest version

3. **Try building without cache:**
   ```bash
   docker build --no-cache --platform linux/amd64 -t doc-qa-frontend:latest ./frontend
   ```

4. **Check Node.js version compatibility** in the Dockerfile
