-- 数据库初始化脚本
-- 创建项目管理系统的核心表结构

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建项目表
CREATE TABLE IF NOT EXISTS projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    document_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE
);

-- 创建部分唯一索引（只对活跃项目的名称唯一）
CREATE UNIQUE INDEX IF NOT EXISTS idx_projects_name_unique 
ON projects (name) WHERE is_active = TRUE;

-- 创建文档表
CREATE TABLE IF NOT EXISTS documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    filename VARCHAR(500) NOT NULL,
    original_name VARCHAR(500) NOT NULL,
    file_path TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'uploaded',
    upload_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    vectorize_time TIMESTAMP WITH TIME ZONE,
    chunk_count INTEGER DEFAULT 0,
    file_size BIGINT DEFAULT 0,
    project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
    
    -- 文档摘要字段
    summary_project_background TEXT DEFAULT '',
    summary_product_requirements TEXT DEFAULT '',
    summary_core_functions TEXT DEFAULT '',
    summary_target_users TEXT DEFAULT '',
    
    -- 重复检测结果字段
    duplicate_is_duplicate BOOLEAN DEFAULT FALSE,
    duplicate_needs_review BOOLEAN DEFAULT FALSE,
    duplicate_similarity_score FLOAT DEFAULT 0.0,
    duplicate_document_id UUID,
    duplicate_document_name VARCHAR(500),
    duplicate_similar_content JSONB DEFAULT '[]',
    duplicate_detection_time TIMESTAMP WITH TIME ZONE,
    duplicate_ai_analysis JSONB DEFAULT '{}'
);

-- 创建聊天会话表
CREATE TABLE IF NOT EXISTS chat_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- 创建聊天消息表
CREATE TABLE IF NOT EXISTS chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    message_type VARCHAR(20) NOT NULL CHECK (message_type IN ('user', 'assistant')),
    content TEXT NOT NULL,
    sources JSONB DEFAULT '[]',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
    tokens_used INTEGER DEFAULT 0,
    
    FOREIGN KEY (session_id) REFERENCES chat_sessions(session_id) ON DELETE CASCADE
);

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建数据库操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id SERIAL PRIMARY KEY,
    operation_type VARCHAR(50) NOT NULL,
    table_name VARCHAR(100),
    record_id UUID,
    old_data JSONB,
    new_data JSONB,
    user_id VARCHAR(100),
    ip_address INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_projects_name ON projects(name);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_projects_is_active ON projects(is_active);

CREATE INDEX IF NOT EXISTS idx_documents_project_id ON documents(project_id);
CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
CREATE INDEX IF NOT EXISTS idx_documents_upload_time ON documents(upload_time DESC);
CREATE INDEX IF NOT EXISTS idx_documents_filename ON documents(filename);
CREATE INDEX IF NOT EXISTS idx_documents_duplicate_score ON documents(duplicate_similarity_score);

CREATE INDEX IF NOT EXISTS idx_chat_sessions_project_id ON chat_sessions(project_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_session_id ON chat_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_created_at ON chat_sessions(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_project_id ON chat_messages(project_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_timestamp ON chat_messages(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_chat_messages_type ON chat_messages(message_type);

CREATE INDEX IF NOT EXISTS idx_operation_logs_table_record ON operation_logs(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_operation_logs_created_at ON operation_logs(created_at DESC);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
DROP TRIGGER IF EXISTS update_projects_updated_at ON projects;
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_chat_sessions_updated_at ON chat_sessions;
CREATE TRIGGER update_chat_sessions_updated_at BEFORE UPDATE ON chat_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 删除已存在的触发器（如果存在）
DROP TRIGGER IF EXISTS update_system_config_updated_at ON system_config;
CREATE TRIGGER update_system_config_updated_at BEFORE UPDATE ON system_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认项目
INSERT INTO projects (id, name, description, created_at, updated_at, document_count, is_active)
VALUES (
    '00000000-0000-0000-0000-000000000001'::UUID,
    '默认项目',
    '系统默认项目，用于存放未分类的文档',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    0,
    TRUE
) ON CONFLICT (id) DO NOTHING;

-- 插入系统配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('database_version', '1.0.0', '数据库版本'),
('backup_retention_days', '30', '备份保留天数'),
('max_file_size', '52428800', '最大文件大小（字节）'),
('default_project_id', 'default', '默认项目ID')
ON CONFLICT (config_key) DO NOTHING;

-- 创建数据库统计视图
CREATE OR REPLACE VIEW database_stats AS
SELECT 
    'projects' as table_name,
    COUNT(*) as total_count,
    COUNT(*) FILTER (WHERE is_active = TRUE) as active_count,
    MAX(created_at) as latest_created
FROM projects
UNION ALL
SELECT 
    'documents' as table_name,
    COUNT(*) as total_count,
    COUNT(*) FILTER (WHERE status = 'completed') as active_count,
    MAX(upload_time) as latest_created
FROM documents
UNION ALL
SELECT 
    'chat_sessions' as table_name,
    COUNT(*) as total_count,
    COUNT(*) FILTER (WHERE is_active = TRUE) as active_count,
    MAX(created_at) as latest_created
FROM chat_sessions
UNION ALL
SELECT 
    'chat_messages' as table_name,
    COUNT(*) as total_count,
    COUNT(*) as active_count,
    MAX(timestamp) as latest_created
FROM chat_messages;

-- 创建项目统计视图
CREATE OR REPLACE VIEW project_stats AS
SELECT 
    p.id,
    p.name,
    p.description,
    p.created_at,
    p.updated_at,
    COUNT(d.id) as actual_document_count,
    COUNT(d.id) FILTER (WHERE d.status = 'completed') as completed_documents,
    COUNT(cs.id) as chat_sessions_count,
    COUNT(cm.id) as chat_messages_count,
    MAX(d.upload_time) as latest_document_upload,
    MAX(cs.created_at) as latest_chat_session
FROM projects p
LEFT JOIN documents d ON p.id = d.project_id
LEFT JOIN chat_sessions cs ON p.id = cs.project_id
LEFT JOIN chat_messages cm ON p.id = cm.project_id
WHERE p.is_active = TRUE
GROUP BY p.id, p.name, p.description, p.created_at, p.updated_at
ORDER BY p.created_at DESC;

-- 创建文档重复检测统计视图
CREATE OR REPLACE VIEW duplicate_detection_stats AS
SELECT 
    COUNT(*) as total_documents,
    COUNT(*) FILTER (WHERE duplicate_is_duplicate = TRUE) as duplicate_count,
    COUNT(*) FILTER (WHERE duplicate_needs_review = TRUE) as needs_review_count,
    AVG(duplicate_similarity_score) as avg_similarity_score,
    MAX(duplicate_similarity_score) as max_similarity_score,
    COUNT(*) FILTER (WHERE duplicate_detection_time IS NOT NULL) as processed_count
FROM documents;

COMMIT;