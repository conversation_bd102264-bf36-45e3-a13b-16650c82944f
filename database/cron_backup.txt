# PostgreSQL数据库定期备份Cron配置
# 将此内容添加到系统crontab中以启用自动备份
#
# 使用方法:
# 1. 编辑当前用户的crontab: crontab -e
# 2. 将下面的配置行添加到crontab文件中
# 3. 保存并退出编辑器
#
# 或者使用命令直接添加:
# crontab -l > /tmp/current_cron
# cat /Users/<USER>/Downloads/doc-qa-system/database/cron_backup.txt >> /tmp/current_cron
# crontab /tmp/current_cron
# rm /tmp/current_cron

# 每天凌晨2点执行数据库备份
0 2 * * * /Users/<USER>/Downloads/doc-qa-system/database/backup_database.sh >> /Users/<USER>/Downloads/doc-qa-system/database/backups/backup.log 2>&1

# 每周日凌晨3点执行额外的完整备份（可选）
0 3 * * 0 /Users/<USER>/Downloads/doc-qa-system/database/backup_database.sh >> /Users/<USER>/Downloads/doc-qa-system/database/backups/weekly_backup.log 2>&1

# 每月1号凌晨4点清理超过90天的备份文件（可选）
0 4 1 * * find /Users/<USER>/Downloads/doc-qa-system/database/backups -name "backup_doc_qa_system_*.sql.gz" -type f -mtime +90 -delete >> /Users/<USER>/Downloads/doc-qa-system/database/backups/cleanup.log 2>&1

# 备份频率说明:
# 分 时 日 月 周 命令
# 0 2 * * *     每天凌晨2点
# 0 */6 * * *   每6小时一次
# 0 2 * * 1-5   工作日凌晨2点
# 0 2 1 * *     每月1号凌晨2点