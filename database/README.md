# PostgreSQL数据库解决方案

本目录包含了项目管理系统的PostgreSQL数据库解决方案，提供完整的数据持久化、备份恢复、性能优化和监控维护功能。

## 目录结构

```
database/
├── README.md                    # 本文档
├── docker-compose.yml           # PostgreSQL Docker配置（在根目录）
├── init/                        # 数据库初始化脚本
│   ├── 01_init_database.sql     # 数据库表结构初始化
│   └── 02_performance_config.sql # 性能优化配置
├── migrations/                  # 数据库迁移脚本
├── backups/                     # 数据库备份文件存储
├── logs/                        # 日志文件存储
├── backup_database.sh           # 数据库备份脚本
├── restore_database.sh          # 数据库恢复脚本
├── monitor_database.sh          # 数据库监控脚本
├── maintenance_database.sh      # 数据库维护脚本
├── postgresql_tuning.conf       # PostgreSQL性能优化配置
├── cron_backup.txt             # 备份定时任务配置
└── cron_monitoring.txt         # 监控维护定时任务配置
```

## 快速开始

### 1. 启动PostgreSQL服务

```bash
# 在项目根目录执行
docker-compose up -d postgres
```

### 2. 验证数据库连接

```bash
# 测试数据库连接
./database/monitor_database.sh
```

### 3. 查看数据库状态

```bash
# 检查数据库健康状态
./database/maintenance_database.sh health
```

## 功能特性

### 🗄️ 数据持久化

- **容器化部署**: 使用Docker容器部署PostgreSQL服务
- **数据卷持久化**: 数据存储在Docker卷中，确保容器重启后数据不丢失
- **表结构设计**: 完整的项目管理系统表结构，包括项目、文档、聊天等核心功能
- **索引优化**: 针对查询性能优化的索引设计

### 💾 备份恢复

- **自动备份**: 支持定时自动备份，保留指定天数的备份文件
- **压缩存储**: 备份文件自动压缩，节省存储空间
- **备份验证**: 自动验证备份文件完整性
- **快速恢复**: 提供便捷的数据库恢复功能

### 📊 性能监控

- **实时监控**: 监控连接数、慢查询、缓存命中率等关键指标
- **性能报告**: 定期生成详细的性能分析报告
- **告警机制**: 当指标超过阈值时自动记录告警信息
- **历史趋势**: 保留监控历史数据，便于分析趋势

### 🔧 维护优化

- **自动维护**: 定期执行VACUUM、ANALYZE、REINDEX等维护操作
- **数据清理**: 自动清理过期数据和日志文件
- **性能调优**: 提供PostgreSQL性能优化配置模板
- **健康检查**: 定期检查数据库健康状态

## 使用指南

### 数据库备份

```bash
# 手动执行备份
./database/backup_database.sh

# 设置定时备份（每天凌晨2点）
crontab -e
# 添加以下行：
# 0 2 * * * /path/to/backup_database.sh
```

### 数据库恢复

```bash
# 列出可用的备份文件
./database/restore_database.sh --list

# 从指定备份文件恢复
./database/restore_database.sh backup_doc_qa_system_20240115_020001.sql.gz

# 从最新备份恢复
./database/restore_database.sh --latest
```

### 性能监控

```bash
# 执行基本监控检查
./database/monitor_database.sh

# 生成详细性能报告
./database/monitor_database.sh --report

# 查看监控日志
tail -f database/logs/monitor_$(date +%Y%m%d).log
```

### 数据库维护

```bash
# 执行VACUUM操作
./database/maintenance_database.sh vacuum

# 更新统计信息
./database/maintenance_database.sh analyze

# 重建索引
./database/maintenance_database.sh reindex

# 清理过期数据
./database/maintenance_database.sh cleanup

# 执行完整维护
./database/maintenance_database.sh full
```

## 配置说明

### 数据库连接配置

在 `backend/app/config.py` 中配置数据库连接参数：

```python
class Settings(BaseSettings):
    # PostgreSQL配置
    postgres_host: str = "localhost"
    postgres_port: int = 5432
    postgres_db: str = "doc_qa_system"
    postgres_user: str = "postgres"
    postgres_password: str = "postgres123"
    
    # 连接池配置
    postgres_pool_size: int = 10
    postgres_max_overflow: int = 20
    postgres_pool_timeout: int = 30
    postgres_pool_recycle: int = 3600
```

### 性能优化配置

参考 `postgresql_tuning.conf` 文件中的配置参数，根据服务器硬件配置进行调整：

- **内存配置**: shared_buffers, work_mem, maintenance_work_mem
- **连接配置**: max_connections, connection_timeout
- **查询优化**: random_page_cost, effective_cache_size
- **WAL配置**: wal_buffers, checkpoint_completion_target

### 监控阈值配置

在监控脚本中可以调整以下阈值：

```bash
# monitor_database.sh 中的配置
MAX_CONNECTIONS_THRESHOLD=80      # 连接数阈值（百分比）
SLOW_QUERY_THRESHOLD=1000         # 慢查询阈值（毫秒）
CACHE_HIT_RATIO_THRESHOLD=95      # 缓存命中率阈值（百分比）
```

## 定时任务配置

### 备份任务

```bash
# 每天凌晨2点备份
0 2 * * * /path/to/backup_database.sh

# 每周日凌晨3点完整备份
0 3 * * 0 /path/to/backup_database.sh
```

### 监控任务

```bash
# 每15分钟监控检查
*/15 * * * * /path/to/monitor_database.sh

# 每小时生成性能报告
0 * * * * /path/to/monitor_database.sh --report
```

### 维护任务

```bash
# 每天凌晨1点维护
0 1 * * * /path/to/maintenance_database.sh vacuum
30 1 * * * /path/to/maintenance_database.sh analyze

# 每周六完整维护
0 4 * * 6 /path/to/maintenance_database.sh full
```

## 故障排除

### 常见问题

1. **连接失败**
   ```bash
   # 检查PostgreSQL服务状态
   docker-compose ps postgres
   
   # 查看PostgreSQL日志
   docker-compose logs postgres
   ```

2. **备份失败**
   ```bash
   # 检查磁盘空间
   df -h
   
   # 检查备份目录权限
   ls -la database/backups/
   ```

3. **性能问题**
   ```bash
   # 查看慢查询
   ./database/monitor_database.sh --report
   
   # 检查数据库统计信息
   ./database/maintenance_database.sh analyze
   ```

### 日志文件

- **备份日志**: `database/logs/backup_*.log`
- **监控日志**: `database/logs/monitor_*.log`
- **维护日志**: `database/logs/maintenance_*.log`
- **告警日志**: `database/logs/alerts_*.log`

## 安全建议

1. **密码安全**
   - 使用强密码
   - 定期更换数据库密码
   - 不要在脚本中硬编码密码

2. **网络安全**
   - 限制数据库访问IP
   - 使用SSL连接（生产环境）
   - 配置防火墙规则

3. **备份安全**
   - 加密备份文件
   - 异地存储备份
   - 定期测试恢复流程

## 性能优化建议

1. **硬件优化**
   - 使用SSD存储
   - 增加内存容量
   - 使用多核CPU

2. **配置优化**
   - 调整shared_buffers
   - 优化work_mem设置
   - 配置合适的连接池大小

3. **查询优化**
   - 创建合适的索引
   - 优化SQL查询
   - 定期更新统计信息

## 扩展功能

### 主从复制

如需要高可用性，可以配置PostgreSQL主从复制：

```yaml
# docker-compose.yml 中添加从库配置
postgres-slave:
  image: postgres:15
  environment:
    POSTGRES_USER: postgres
    POSTGRES_PASSWORD: postgres123
    PGUSER: postgres
  command: |
    postgres 
    -c wal_level=replica 
    -c max_wal_senders=3 
    -c max_replication_slots=3
```

### 连接池

可以使用PgBouncer作为连接池：

```yaml
pgbouncer:
  image: pgbouncer/pgbouncer:latest
  environment:
    DATABASES_HOST: postgres
    DATABASES_PORT: 5432
    DATABASES_USER: postgres
    DATABASES_PASSWORD: postgres123
    DATABASES_DBNAME: doc_qa_system
  ports:
    - "6432:5432"
```

## 联系支持

如果遇到问题或需要技术支持，请：

1. 查看日志文件获取详细错误信息
2. 检查配置文件是否正确
3. 参考PostgreSQL官方文档
4. 联系系统管理员

---

**注意**: 在生产环境中使用前，请务必在测试环境中验证所有功能，并根据实际需求调整配置参数。