#!/bin/bash

# PostgreSQL数据库维护脚本
# 用于定期维护数据库，包括VACUUM、ANALYZE、REINDEX等操作

# 配置变量
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="doc_qa_system"
DB_USER="postgres"
DB_PASSWORD="postgres123"

# 日志配置
LOG_DIR="$(dirname "$0")/logs"
LOG_FILE="$LOG_DIR/maintenance_$(date +%Y%m%d).log"

# 维护配置
VACUUM_THRESHOLD=1000000      # 表行数超过此值时执行VACUUM
REINDEX_THRESHOLD=10000000    # 表行数超过此值时考虑REINDEX
ANALYZE_ALL_TABLES=true       # 是否分析所有表
VACUUM_FULL=false             # 是否执行VACUUM FULL（会锁表）

# 创建日志目录
mkdir -p "$LOG_DIR"

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1" | tee -a "$LOG_FILE"
}

log_warn() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] WARN: $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$LOG_FILE"
}

# 检查依赖
check_dependencies() {
    if ! command -v psql &> /dev/null; then
        log_error "psql命令未找到，请安装PostgreSQL客户端"
        exit 1
    fi
}

# 测试数据库连接
test_connection() {
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &> /dev/null; then
        log_info "数据库连接正常"
        return 0
    else
        log_error "无法连接到数据库"
        return 1
    fi
}

# 执行SQL查询
execute_query() {
    local query="$1"
    local description="$2"
    
    log_info "执行: $description"
    
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$query" >> "$LOG_FILE" 2>&1; then
        log_info "完成: $description"
        return 0
    else
        log_error "失败: $description"
        return 1
    fi
}

# 获取表统计信息
get_table_stats() {
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT 
            schemaname,
            tablename,
            n_live_tup,
            n_dead_tup,
            last_vacuum,
            last_autovacuum,
            last_analyze,
            last_autoanalyze
        FROM pg_stat_user_tables 
        ORDER BY n_live_tup DESC;
    " 2>/dev/null
}

# 执行VACUUM操作
perform_vacuum() {
    log_info "开始VACUUM操作..."
    
    # 获取需要VACUUM的表
    local tables_to_vacuum
    tables_to_vacuum=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT tablename 
        FROM pg_stat_user_tables 
        WHERE n_dead_tup > 1000 OR last_vacuum IS NULL OR last_vacuum < NOW() - INTERVAL '7 days'
        ORDER BY n_dead_tup DESC;
    " 2>/dev/null | tr -d ' ')
    
    if [[ -z "$tables_to_vacuum" ]]; then
        log_info "没有表需要VACUUM"
        return 0
    fi
    
    # 对每个表执行VACUUM
    while IFS= read -r table; do
        if [[ -n "$table" ]]; then
            if [[ "$VACUUM_FULL" == "true" ]]; then
                execute_query "VACUUM FULL ANALYZE $table;" "VACUUM FULL表: $table"
            else
                execute_query "VACUUM ANALYZE $table;" "VACUUM表: $table"
            fi
        fi
    done <<< "$tables_to_vacuum"
    
    log_info "VACUUM操作完成"
}

# 执行ANALYZE操作
perform_analyze() {
    log_info "开始ANALYZE操作..."
    
    if [[ "$ANALYZE_ALL_TABLES" == "true" ]]; then
        execute_query "ANALYZE;" "分析所有表的统计信息"
    else
        # 只分析需要更新统计信息的表
        local tables_to_analyze
        tables_to_analyze=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
            SELECT tablename 
            FROM pg_stat_user_tables 
            WHERE last_analyze IS NULL OR last_analyze < NOW() - INTERVAL '7 days'
            ORDER BY n_live_tup DESC;
        " 2>/dev/null | tr -d ' ')
        
        if [[ -z "$tables_to_analyze" ]]; then
            log_info "没有表需要ANALYZE"
            return 0
        fi
        
        while IFS= read -r table; do
            if [[ -n "$table" ]]; then
                execute_query "ANALYZE $table;" "分析表: $table"
            fi
        done <<< "$tables_to_analyze"
    fi
    
    log_info "ANALYZE操作完成"
}

# 执行REINDEX操作
perform_reindex() {
    log_info "开始REINDEX操作..."
    
    # 获取需要重建索引的表（基于表大小和索引膨胀）
    local tables_to_reindex
    tables_to_reindex=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT t.tablename
        FROM pg_stat_user_tables t
        JOIN pg_class c ON c.relname = t.tablename
        WHERE t.n_live_tup > $REINDEX_THRESHOLD
        AND c.relkind = 'r'
        ORDER BY t.n_live_tup DESC
        LIMIT 5;
    " 2>/dev/null | tr -d ' ')
    
    if [[ -z "$tables_to_reindex" ]]; then
        log_info "没有表需要REINDEX"
        return 0
    fi
    
    # 对每个表执行REINDEX
    while IFS= read -r table; do
        if [[ -n "$table" ]]; then
            execute_query "REINDEX TABLE $table;" "重建表索引: $table"
        fi
    done <<< "$tables_to_reindex"
    
    log_info "REINDEX操作完成"
}

# 清理过期数据
cleanup_old_data() {
    log_info "开始清理过期数据..."
    
    # 清理超过30天的操作日志
    execute_query "
        DELETE FROM operation_logs 
        WHERE created_at < NOW() - INTERVAL '30 days';
    " "清理30天前的操作日志"
    
    # 清理超过7天的已删除项目数据
    execute_query "
        DELETE FROM projects 
        WHERE status = 'deleted' AND updated_at < NOW() - INTERVAL '7 days';
    " "清理7天前的已删除项目"
    
    # 清理孤立的聊天消息（没有对应会话的消息）
    execute_query "
        DELETE FROM chat_messages 
        WHERE session_id NOT IN (SELECT id FROM chat_sessions);
    " "清理孤立的聊天消息"
    
    log_info "过期数据清理完成"
}

# 更新表统计信息
update_statistics() {
    log_info "更新数据库统计信息..."
    
    # 更新pg_stat_statements统计信息
    execute_query "SELECT pg_stat_statements_reset();" "重置查询统计信息"
    
    # 收集表和索引的统计信息
    execute_query "
        SELECT 
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as table_size,
            n_live_tup as live_rows,
            n_dead_tup as dead_rows
        FROM pg_stat_user_tables 
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
    " "收集表大小统计信息"
    
    log_info "统计信息更新完成"
}

# 检查数据库健康状态
check_database_health() {
    log_info "检查数据库健康状态..."
    
    # 检查数据库连接数
    local connections
    connections=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT count(*) FROM pg_stat_activity WHERE state = 'active';
    " 2>/dev/null | tr -d ' ')
    
    log_info "当前活跃连接数: $connections"
    
    # 检查锁等待
    local blocked_queries
    blocked_queries=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT count(*) FROM pg_stat_activity WHERE wait_event_type = 'Lock';
    " 2>/dev/null | tr -d ' ')
    
    if [[ "$blocked_queries" -gt 0 ]]; then
        log_warn "发现 $blocked_queries 个被锁阻塞的查询"
    else
        log_info "没有发现锁等待"
    fi
    
    # 检查数据库大小
    local db_size
    db_size=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT pg_size_pretty(pg_database_size('$DB_NAME'));
    " 2>/dev/null | tr -d ' ')
    
    log_info "数据库大小: $db_size"
    
    log_info "数据库健康检查完成"
}

# 生成维护报告
generate_maintenance_report() {
    log_info "生成维护报告..."
    
    local report_file="$LOG_DIR/maintenance_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "PostgreSQL数据库维护报告 - $(date)"
        echo "=========================================="
        echo ""
        
        echo "维护前表统计信息:"
        get_table_stats
        echo ""
        
        echo "维护操作日志:"
        tail -n 50 "$LOG_FILE"
        echo ""
        
        echo "维护后数据库状态:"
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
            SELECT 
                'Database Size' as metric,
                pg_size_pretty(pg_database_size('$DB_NAME')) as value
            UNION ALL
            SELECT 
                'Total Tables' as metric,
                count(*)::text as value
            FROM pg_stat_user_tables
            UNION ALL
            SELECT 
                'Active Connections' as metric,
                count(*)::text as value
            FROM pg_stat_activity 
            WHERE state = 'active';
        " 2>/dev/null
        
    } > "$report_file"
    
    log_info "维护报告已生成: $report_file"
}

# 主函数
main() {
    local operation="$1"
    
    log_info "开始数据库维护操作: $operation"
    
    # 检查依赖
    check_dependencies
    
    # 测试连接
    if ! test_connection; then
        exit 1
    fi
    
    case "$operation" in
        "vacuum")
            perform_vacuum
            ;;
        "analyze")
            perform_analyze
            ;;
        "reindex")
            perform_reindex
            ;;
        "cleanup")
            cleanup_old_data
            ;;
        "health")
            check_database_health
            ;;
        "full")
            perform_vacuum
            perform_analyze
            cleanup_old_data
            update_statistics
            check_database_health
            generate_maintenance_report
            ;;
        *)
            echo "用法: $0 {vacuum|analyze|reindex|cleanup|health|full}"
            echo ""
            echo "操作说明:"
            echo "  vacuum   - 执行VACUUM操作，清理死元组"
            echo "  analyze  - 执行ANALYZE操作，更新统计信息"
            echo "  reindex  - 重建索引"
            echo "  cleanup  - 清理过期数据"
            echo "  health   - 检查数据库健康状态"
            echo "  full     - 执行完整的维护操作"
            echo ""
            exit 1
            ;;
    esac
    
    log_info "数据库维护操作完成: $operation"
}

# 执行主函数
main "$@"