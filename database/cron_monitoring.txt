# PostgreSQL数据库监控和维护定时任务配置
# 将此内容添加到系统crontab中以启用自动监控和维护
#
# 使用方法:
# 1. 编辑当前用户的crontab: crontab -e
# 2. 将下面的配置行添加到crontab文件中
# 3. 保存并退出编辑器
#
# 或者使用命令直接添加:
# crontab -l > /tmp/current_cron
# cat /Users/<USER>/Downloads/doc-qa-system/database/cron_monitoring.txt >> /tmp/current_cron
# crontab /tmp/current_cron
# rm /tmp/current_cron

# =============================================================================
# 数据库备份任务
# =============================================================================

# 每天凌晨2点执行数据库备份
0 2 * * * /Users/<USER>/Downloads/doc-qa-system/database/backup_database.sh >> /Users/<USER>/Downloads/doc-qa-system/database/logs/backup.log 2>&1

# 每周日凌晨3点执行额外的完整备份
0 3 * * 0 /Users/<USER>/Downloads/doc-qa-system/database/backup_database.sh >> /Users/<USER>/Downloads/doc-qa-system/database/logs/weekly_backup.log 2>&1

# =============================================================================
# 数据库监控任务
# =============================================================================

# 每15分钟执行一次基本监控检查
*/15 * * * * /Users/<USER>/Downloads/doc-qa-system/database/monitor_database.sh >> /Users/<USER>/Downloads/doc-qa-system/database/logs/monitor.log 2>&1

# 每小时生成一次性能报告
0 * * * * /Users/<USER>/Downloads/doc-qa-system/database/monitor_database.sh --report >> /Users/<USER>/Downloads/doc-qa-system/database/logs/performance_report.log 2>&1

# 每天早上8点发送监控摘要（需要配置邮件服务）
# 0 8 * * * /Users/<USER>/Downloads/doc-qa-system/database/monitor_database.sh --report | mail -s "数据库监控日报" <EMAIL>

# =============================================================================
# 数据库维护任务
# =============================================================================

# 每天凌晨1点执行VACUUM和ANALYZE
0 1 * * * /Users/<USER>/Downloads/doc-qa-system/database/maintenance_database.sh vacuum >> /Users/<USER>/Downloads/doc-qa-system/database/logs/maintenance.log 2>&1

# 每天凌晨1:30执行统计信息更新
30 1 * * * /Users/<USER>/Downloads/doc-qa-system/database/maintenance_database.sh analyze >> /Users/<USER>/Downloads/doc-qa-system/database/logs/maintenance.log 2>&1

# 每周六凌晨4点执行完整维护
0 4 * * 6 /Users/<USER>/Downloads/doc-qa-system/database/maintenance_database.sh full >> /Users/<USER>/Downloads/doc-qa-system/database/logs/full_maintenance.log 2>&1

# 每月1号凌晨5点执行REINDEX操作
0 5 1 * * /Users/<USER>/Downloads/doc-qa-system/database/maintenance_database.sh reindex >> /Users/<USER>/Downloads/doc-qa-system/database/logs/reindex.log 2>&1

# 每天凌晨6点清理过期数据
0 6 * * * /Users/<USER>/Downloads/doc-qa-system/database/maintenance_database.sh cleanup >> /Users/<USER>/Downloads/doc-qa-system/database/logs/cleanup.log 2>&1

# =============================================================================
# 日志清理任务
# =============================================================================

# 每周日凌晨7点清理超过30天的日志文件
0 7 * * 0 find /Users/<USER>/Downloads/doc-qa-system/database/logs -name "*.log" -type f -mtime +30 -delete

# 每月1号清理超过90天的备份文件
0 4 1 * * find /Users/<USER>/Downloads/doc-qa-system/database/backups -name "backup_doc_qa_system_*.sql.gz" -type f -mtime +90 -delete >> /Users/<USER>/Downloads/doc-qa-system/database/logs/cleanup.log 2>&1

# 每月1号清理超过60天的性能报告
0 4 1 * * find /Users/<USER>/Downloads/doc-qa-system/database/logs -name "performance_report_*.txt" -type f -mtime +60 -delete

# =============================================================================
# 健康检查任务
# =============================================================================

# 每5分钟检查数据库连接状态
*/5 * * * * /Users/<USER>/Downloads/doc-qa-system/database/maintenance_database.sh health >> /Users/<USER>/Downloads/doc-qa-system/database/logs/health_check.log 2>&1

# 每小时检查磁盘空间使用情况
0 * * * * df -h /Users/<USER>/Downloads/doc-qa-system/database >> /Users/<USER>/Downloads/doc-qa-system/database/logs/disk_usage.log 2>&1

# =============================================================================
# 自定义监控任务（可选）
# =============================================================================

# 每30分钟检查慢查询
# */30 * * * * psql -h localhost -U postgres -d doc_qa_system -c "SELECT query, mean_exec_time FROM pg_stat_statements WHERE mean_exec_time > 1000 ORDER BY mean_exec_time DESC LIMIT 5;" >> /Users/<USER>/Downloads/doc-qa-system/database/logs/slow_queries.log 2>&1

# 每小时检查数据库大小增长
# 0 * * * * psql -h localhost -U postgres -d doc_qa_system -c "SELECT pg_size_pretty(pg_database_size('doc_qa_system'));" >> /Users/<USER>/Downloads/doc-qa-system/database/logs/db_size.log 2>&1

# 每天检查表膨胀情况
# 0 12 * * * psql -h localhost -U postgres -d doc_qa_system -c "SELECT schemaname, tablename, n_dead_tup, n_live_tup, CASE WHEN n_live_tup > 0 THEN round(n_dead_tup::numeric / n_live_tup::numeric * 100, 2) ELSE 0 END as bloat_ratio FROM pg_stat_user_tables WHERE n_dead_tup > 1000 ORDER BY bloat_ratio DESC;" >> /Users/<USER>/Downloads/doc-qa-system/database/logs/table_bloat.log 2>&1

# =============================================================================
# 时间格式说明
# =============================================================================
# 分 时 日 月 周 命令
# *  *  *  *  *  
# |  |  |  |  |
# |  |  |  |  +-- 星期几 (0-7, 0和7都表示星期日)
# |  |  |  +----- 月份 (1-12)
# |  |  +-------- 日期 (1-31)
# |  +----------- 小时 (0-23)
# +-------------- 分钟 (0-59)
#
# 特殊字符:
# * : 匹配任何值
# , : 分隔多个值
# - : 表示范围
# / : 表示步长
#
# 示例:
# */15 * * * *    每15分钟
# 0 2 * * *       每天凌晨2点
# 0 2 * * 1-5     工作日凌晨2点
# 0 2 1 * *       每月1号凌晨2点
# 0 */6 * * *     每6小时

# =============================================================================
# 安装说明
# =============================================================================
# 1. 确保所有脚本文件具有执行权限:
#    chmod +x /Users/<USER>/Downloads/doc-qa-system/database/*.sh
#
# 2. 创建必要的日志目录:
#    mkdir -p /Users/<USER>/Downloads/doc-qa-system/database/logs
#
# 3. 测试脚本是否正常工作:
#    /Users/<USER>/Downloads/doc-qa-system/database/monitor_database.sh
#    /Users/<USER>/Downloads/doc-qa-system/database/maintenance_database.sh health
#
# 4. 添加到crontab:
#    crontab -e
#    # 复制上述配置到crontab文件中
#
# 5. 验证crontab配置:
#    crontab -l
#
# 6. 检查cron服务状态:
#    # macOS: sudo launchctl list | grep cron
#    # Linux: systemctl status cron