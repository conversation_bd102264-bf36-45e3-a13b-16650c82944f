#!/bin/bash

# PostgreSQL数据库恢复脚本
# 用于从备份文件恢复项目管理系统数据库

set -e  # 遇到错误立即退出

# 配置变量
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="doc_qa_system"
DB_USER="doc_qa_user"
DB_PASSWORD="doc_qa_password_2024"
BACKUP_DIR="/Users/<USER>/Downloads/doc-qa-system/database/backups"
TEMP_DIR="/tmp"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 [选项] <备份文件路径>"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -l, --list              列出可用的备份文件"
    echo "  -f, --force             强制恢复（不询问确认）"
    echo "  --host HOST             数据库主机 (默认: $DB_HOST)"
    echo "  --port PORT             数据库端口 (默认: $DB_PORT)"
    echo "  --dbname DBNAME         数据库名称 (默认: $DB_NAME)"
    echo "  --username USERNAME     数据库用户 (默认: $DB_USER)"
    echo ""
    echo "示例:"
    echo "  $0 /path/to/backup_file.sql.gz"
    echo "  $0 --list"
    echo "  $0 --force backup_doc_qa_system_20240115_143022.sql.gz"
    echo ""
}

# 列出可用的备份文件
list_backups() {
    log "可用的备份文件:"
    echo ""
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log "备份目录不存在: $BACKUP_DIR"
        exit 1
    fi
    
    # 查找备份文件并按时间排序
    find "$BACKUP_DIR" -name "backup_${DB_NAME}_*.sql.gz" -type f -exec ls -lh {} \; | \
        sort -k9 -r | \
        awk '{print $9 "\t" $5 "\t" $6 " " $7 " " $8}' | \
        while read file size date; do
            basename_file=$(basename "$file")
            echo "  $basename_file\t$size\t$date"
        done
    
    echo ""
    log "使用 '$0 <备份文件名>' 来恢复数据库"
}

# 检查必要的工具
check_dependencies() {
    if ! command -v psql &> /dev/null; then
        log "错误: psql 未找到，请安装 PostgreSQL 客户端工具"
        exit 1
    fi
    
    if ! command -v gzip &> /dev/null; then
        log "错误: gzip 未找到"
        exit 1
    fi
}

# 验证备份文件
validate_backup_file() {
    local backup_file=$1
    
    # 检查文件是否存在
    if [ ! -f "$backup_file" ]; then
        log "错误: 备份文件不存在: $backup_file"
        exit 1
    fi
    
    # 检查文件是否为压缩格式
    if [[ "$backup_file" == *.gz ]]; then
        log "检测到压缩备份文件"
        
        # 验证压缩文件完整性
        if ! gzip -t "$backup_file" 2>/dev/null; then
            log "错误: 备份文件损坏或不是有效的gzip文件"
            exit 1
        fi
        
        log "备份文件完整性验证通过"
    else
        log "检测到未压缩备份文件"
    fi
    
    # 显示文件信息
    local file_size=$(du -h "$backup_file" | cut -f1)
    log "备份文件大小: $file_size"
}

# 测试数据库连接
test_connection() {
    log "测试数据库连接..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="postgres" --command="SELECT 1;" >/dev/null 2>&1; then
        log "数据库连接测试成功"
    else
        log "错误: 无法连接到数据库服务器"
        unset PGPASSWORD
        exit 1
    fi
    
    unset PGPASSWORD
}

# 创建数据库备份（恢复前）
create_pre_restore_backup() {
    log "创建恢复前备份..."
    
    local pre_backup_file="${BACKUP_DIR}/pre_restore_backup_$(date +"%Y%m%d_%H%M%S").sql"
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # 检查数据库是否存在
    if psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="postgres" \
           --tuples-only --no-align --command="SELECT 1 FROM pg_database WHERE datname='$DB_NAME';" | grep -q 1; then
        
        log "数据库存在，创建恢复前备份: $pre_backup_file"
        
        pg_dump \
            --host="$DB_HOST" \
            --port="$DB_PORT" \
            --username="$DB_USER" \
            --dbname="$DB_NAME" \
            --clean \
            --if-exists \
            --create \
            --format=plain \
            --encoding=UTF8 \
            --no-password \
            --file="$pre_backup_file"
        
        gzip "$pre_backup_file"
        log "恢复前备份完成: ${pre_backup_file}.gz"
    else
        log "目标数据库不存在，跳过恢复前备份"
    fi
    
    unset PGPASSWORD
}

# 执行数据库恢复
perform_restore() {
    local backup_file=$1
    local temp_sql_file="${TEMP_DIR}/restore_$(date +"%Y%m%d_%H%M%S").sql"
    
    log "开始恢复数据库: $DB_NAME"
    
    # 解压备份文件（如果需要）
    if [[ "$backup_file" == *.gz ]]; then
        log "解压备份文件到临时位置..."
        gzip -dc "$backup_file" > "$temp_sql_file"
    else
        temp_sql_file="$backup_file"
    fi
    
    # 设置密码环境变量
    export PGPASSWORD="$DB_PASSWORD"
    
    # 执行恢复
    log "执行SQL恢复脚本..."
    psql \
        --host="$DB_HOST" \
        --port="$DB_PORT" \
        --username="$DB_USER" \
        --dbname="postgres" \
        --echo-errors \
        --file="$temp_sql_file"
    
    # 清除密码环境变量
    unset PGPASSWORD
    
    # 清理临时文件
    if [[ "$backup_file" == *.gz ]] && [ -f "$temp_sql_file" ]; then
        rm -f "$temp_sql_file"
        log "清理临时文件: $temp_sql_file"
    fi
    
    log "数据库恢复完成"
}

# 验证恢复结果
validate_restore() {
    log "验证恢复结果..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # 检查数据库是否存在
    if psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="postgres" \
           --tuples-only --no-align --command="SELECT 1 FROM pg_database WHERE datname='$DB_NAME';" | grep -q 1; then
        log "数据库存在检查: 通过"
    else
        log "错误: 数据库不存在"
        unset PGPASSWORD
        exit 1
    fi
    
    # 检查主要表是否存在
    local tables=("projects" "documents" "chat_sessions" "chat_messages")
    for table in "${tables[@]}"; do
        if psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="$DB_NAME" \
               --tuples-only --no-align --command="SELECT 1 FROM information_schema.tables WHERE table_name='$table';" | grep -q 1; then
            log "表存在检查 [$table]: 通过"
        else
            log "警告: 表不存在 [$table]"
        fi
    done
    
    # 获取基本统计信息
    local project_count=$(psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="$DB_NAME" \
                          --tuples-only --no-align --command="SELECT COUNT(*) FROM projects WHERE is_active = true;" 2>/dev/null || echo "0")
    local document_count=$(psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="$DB_NAME" \
                          --tuples-only --no-align --command="SELECT COUNT(*) FROM documents;" 2>/dev/null || echo "0")
    
    log "恢复后统计: 活跃项目数=$project_count, 文档数=$document_count"
    
    unset PGPASSWORD
    
    log "数据库恢复验证完成"
}

# 主函数
main() {
    local backup_file=""
    local force_restore=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -l|--list)
                list_backups
                exit 0
                ;;
            -f|--force)
                force_restore=true
                shift
                ;;
            --host)
                DB_HOST="$2"
                shift 2
                ;;
            --port)
                DB_PORT="$2"
                shift 2
                ;;
            --dbname)
                DB_NAME="$2"
                shift 2
                ;;
            --username)
                DB_USER="$2"
                shift 2
                ;;
            -*)
                log "错误: 未知选项 $1"
                show_usage
                exit 1
                ;;
            *)
                backup_file="$1"
                shift
                ;;
        esac
    done
    
    # 检查是否提供了备份文件
    if [ -z "$backup_file" ]; then
        log "错误: 请提供备份文件路径"
        show_usage
        exit 1
    fi
    
    # 如果提供的是相对路径，尝试在备份目录中查找
    if [[ "$backup_file" != /* ]] && [ -f "${BACKUP_DIR}/${backup_file}" ]; then
        backup_file="${BACKUP_DIR}/${backup_file}"
    fi
    
    log "=== PostgreSQL 数据库恢复开始 ==="
    log "备份文件: $backup_file"
    log "目标数据库: $DB_NAME@$DB_HOST:$DB_PORT"
    
    # 检查依赖
    check_dependencies
    
    # 验证备份文件
    validate_backup_file "$backup_file"
    
    # 测试数据库连接
    test_connection
    
    # 确认恢复操作
    if [ "$force_restore" = false ]; then
        echo ""
        echo "警告: 此操作将完全替换现有数据库 '$DB_NAME'"
        echo "所有现有数据将被删除并替换为备份中的数据"
        echo ""
        read -p "确定要继续吗? (yes/no): " -r
        if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
            log "恢复操作已取消"
            exit 0
        fi
    fi
    
    # 创建恢复前备份
    create_pre_restore_backup
    
    # 执行恢复
    perform_restore "$backup_file"
    
    # 验证恢复结果
    validate_restore
    
    log "=== PostgreSQL 数据库恢复完成 ==="
}

# 错误处理
trap 'log "错误: 恢复过程中发生异常，退出码: $?"; exit 1' ERR

# 执行主函数
main "$@"