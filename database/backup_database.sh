#!/bin/bash

# PostgreSQL数据库备份脚本
# 用于定期备份项目管理系统数据库

set -e  # 遇到错误立即退出

# 配置变量
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="doc_qa_system"
DB_USER="doc_qa_user"
DB_PASSWORD="doc_qa_password_2024"
BACKUP_DIR="/Users/<USER>/Downloads/doc-qa-system/database/backups"
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="${BACKUP_DIR}/backup_${DB_NAME}_${DATE}.sql"
COMPRESSED_FILE="${BACKUP_FILE}.gz"
RETENTION_DAYS=30

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 检查必要的工具
check_dependencies() {
    if ! command -v pg_dump &> /dev/null; then
        log "错误: pg_dump 未找到，请安装 PostgreSQL 客户端工具"
        exit 1
    fi
    
    if ! command -v gzip &> /dev/null; then
        log "错误: gzip 未找到"
        exit 1
    fi
}

# 创建备份目录
create_backup_dir() {
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
        log "创建备份目录: $BACKUP_DIR"
    fi
}

# 执行数据库备份
perform_backup() {
    log "开始备份数据库: $DB_NAME"
    
    # 设置密码环境变量
    export PGPASSWORD="$DB_PASSWORD"
    
    # 执行备份
    pg_dump \
        --host="$DB_HOST" \
        --port="$DB_PORT" \
        --username="$DB_USER" \
        --dbname="$DB_NAME" \
        --verbose \
        --clean \
        --if-exists \
        --create \
        --format=plain \
        --encoding=UTF8 \
        --no-password \
        --file="$BACKUP_FILE"
    
    # 清除密码环境变量
    unset PGPASSWORD
    
    if [ $? -eq 0 ]; then
        log "数据库备份成功: $BACKUP_FILE"
    else
        log "错误: 数据库备份失败"
        exit 1
    fi
}

# 压缩备份文件
compress_backup() {
    log "压缩备份文件..."
    gzip "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        log "备份文件压缩成功: $COMPRESSED_FILE"
        
        # 显示文件大小
        BACKUP_SIZE=$(du -h "$COMPRESSED_FILE" | cut -f1)
        log "备份文件大小: $BACKUP_SIZE"
    else
        log "错误: 备份文件压缩失败"
        exit 1
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log "清理 $RETENTION_DAYS 天前的旧备份文件..."
    
    # 查找并删除旧备份文件
    find "$BACKUP_DIR" -name "backup_${DB_NAME}_*.sql.gz" -type f -mtime +$RETENTION_DAYS -delete
    
    # 显示剩余备份文件数量
    REMAINING_BACKUPS=$(find "$BACKUP_DIR" -name "backup_${DB_NAME}_*.sql.gz" -type f | wc -l)
    log "剩余备份文件数量: $REMAINING_BACKUPS"
}

# 验证备份文件
validate_backup() {
    log "验证备份文件完整性..."
    
    # 检查压缩文件是否可以正常解压
    if gzip -t "$COMPRESSED_FILE" 2>/dev/null; then
        log "备份文件完整性验证通过"
    else
        log "错误: 备份文件完整性验证失败"
        exit 1
    fi
    
    # 检查备份文件大小
    BACKUP_SIZE_BYTES=$(stat -f%z "$COMPRESSED_FILE" 2>/dev/null || stat -c%s "$COMPRESSED_FILE" 2>/dev/null)
    if [ "$BACKUP_SIZE_BYTES" -lt 1024 ]; then
        log "警告: 备份文件大小异常小 ($BACKUP_SIZE_BYTES bytes)"
    fi
}

# 发送备份状态通知（可选）
send_notification() {
    local status=$1
    local message=$2
    
    # 这里可以添加邮件通知、Slack通知等
    # 例如: echo "$message" | mail -s "Database Backup $status" <EMAIL>
    
    log "备份状态: $status - $message"
}

# 主函数
main() {
    log "=== PostgreSQL 数据库备份开始 ==="
    
    # 检查依赖
    check_dependencies
    
    # 创建备份目录
    create_backup_dir
    
    # 执行备份
    perform_backup
    
    # 压缩备份
    compress_backup
    
    # 验证备份
    validate_backup
    
    # 清理旧备份
    cleanup_old_backups
    
    # 发送成功通知
    send_notification "SUCCESS" "数据库备份成功完成: $COMPRESSED_FILE"
    
    log "=== PostgreSQL 数据库备份完成 ==="
}

# 错误处理
trap 'log "错误: 备份过程中发生异常，退出码: $?"; send_notification "FAILED" "数据库备份失败"; exit 1' ERR

# 执行主函数
main "$@"