#!/bin/bash

# PostgreSQL数据库监控脚本
# 用于监控数据库性能指标和健康状态

# 配置变量
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="doc_qa_system"
DB_USER="postgres"
DB_PASSWORD="postgres123"

# 日志配置
LOG_DIR="$(dirname "$0")/logs"
LOG_FILE="$LOG_DIR/monitor_$(date +%Y%m%d).log"
ALERT_LOG="$LOG_DIR/alerts_$(date +%Y%m%d).log"

# 阈值配置
MAX_CONNECTIONS_THRESHOLD=80  # 最大连接数阈值（百分比）
SLOW_QUERY_THRESHOLD=1000     # 慢查询阈值（毫秒）
DISK_USAGE_THRESHOLD=85       # 磁盘使用率阈值（百分比）
CACHE_HIT_RATIO_THRESHOLD=95  # 缓存命中率阈值（百分比）

# 创建日志目录
mkdir -p "$LOG_DIR"

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1" | tee -a "$LOG_FILE"
}

log_warn() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] WARN: $1" | tee -a "$LOG_FILE" "$ALERT_LOG"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$LOG_FILE" "$ALERT_LOG"
}

# 检查依赖
check_dependencies() {
    if ! command -v psql &> /dev/null; then
        log_error "psql命令未找到，请安装PostgreSQL客户端"
        exit 1
    fi
}

# 测试数据库连接
test_connection() {
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &> /dev/null; then
        log_info "数据库连接正常"
        return 0
    else
        log_error "无法连接到数据库"
        return 1
    fi
}

# 执行SQL查询
execute_query() {
    local query="$1"
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "$query" 2>/dev/null
}

# 监控连接数
monitor_connections() {
    log_info "检查数据库连接数..."
    
    local max_connections=$(execute_query "SHOW max_connections;" | tr -d ' ')
    local current_connections=$(execute_query "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';" | tr -d ' ')
    
    if [[ -n "$max_connections" && -n "$current_connections" ]]; then
        local usage_percent=$((current_connections * 100 / max_connections))
        log_info "当前连接数: $current_connections/$max_connections ($usage_percent%)"
        
        if [[ $usage_percent -gt $MAX_CONNECTIONS_THRESHOLD ]]; then
            log_warn "连接数使用率过高: $usage_percent% (阈值: $MAX_CONNECTIONS_THRESHOLD%)"
        fi
    else
        log_error "无法获取连接数信息"
    fi
}

# 监控慢查询
monitor_slow_queries() {
    log_info "检查慢查询..."
    
    local slow_queries=$(execute_query "
        SELECT count(*) 
        FROM pg_stat_statements 
        WHERE mean_exec_time > $SLOW_QUERY_THRESHOLD;
    " | tr -d ' ')
    
    if [[ -n "$slow_queries" && "$slow_queries" != "0" ]]; then
        log_warn "发现 $slow_queries 个慢查询 (>$SLOW_QUERY_THRESHOLD ms)"
        
        # 获取最慢的5个查询
        execute_query "
            SELECT 
                round(mean_exec_time::numeric, 2) as avg_time_ms,
                calls,
                left(query, 100) as query_preview
            FROM pg_stat_statements 
            WHERE mean_exec_time > $SLOW_QUERY_THRESHOLD
            ORDER BY mean_exec_time DESC 
            LIMIT 5;
        " | while read -r line; do
            log_warn "慢查询: $line"
        done
    else
        log_info "未发现慢查询"
    fi
}

# 监控缓存命中率
monitor_cache_hit_ratio() {
    log_info "检查缓存命中率..."
    
    local cache_hit_ratio=$(execute_query "
        SELECT round(
            sum(blks_hit) * 100.0 / (sum(blks_hit) + sum(blks_read)), 2
        ) as cache_hit_ratio
        FROM pg_stat_database 
        WHERE datname = '$DB_NAME';
    " | tr -d ' ')
    
    if [[ -n "$cache_hit_ratio" ]]; then
        log_info "缓存命中率: $cache_hit_ratio%"
        
        if (( $(echo "$cache_hit_ratio < $CACHE_HIT_RATIO_THRESHOLD" | bc -l) )); then
            log_warn "缓存命中率过低: $cache_hit_ratio% (阈值: $CACHE_HIT_RATIO_THRESHOLD%)"
        fi
    else
        log_error "无法获取缓存命中率"
    fi
}

# 监控数据库大小
monitor_database_size() {
    log_info "检查数据库大小..."
    
    local db_size=$(execute_query "
        SELECT pg_size_pretty(pg_database_size('$DB_NAME'));
    " | tr -d ' ')
    
    if [[ -n "$db_size" ]]; then
        log_info "数据库大小: $db_size"
        
        # 获取最大的5个表
        execute_query "
            SELECT 
                schemaname,
                tablename,
                pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
            FROM pg_tables 
            WHERE schemaname = 'public'
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC 
            LIMIT 5;
        " | while read -r line; do
            log_info "表大小: $line"
        done
    else
        log_error "无法获取数据库大小"
    fi
}

# 监控锁等待
monitor_locks() {
    log_info "检查锁等待..."
    
    local blocked_queries=$(execute_query "
        SELECT count(*) 
        FROM pg_stat_activity 
        WHERE wait_event_type = 'Lock' AND state = 'active';
    " | tr -d ' ')
    
    if [[ -n "$blocked_queries" && "$blocked_queries" != "0" ]]; then
        log_warn "发现 $blocked_queries 个被锁阻塞的查询"
        
        # 获取被阻塞的查询详情
        execute_query "
            SELECT 
                pid,
                usename,
                wait_event,
                left(query, 100) as query_preview
            FROM pg_stat_activity 
            WHERE wait_event_type = 'Lock' AND state = 'active';
        " | while read -r line; do
            log_warn "被阻塞查询: $line"
        done
    else
        log_info "未发现锁等待"
    fi
}

# 生成性能报告
generate_performance_report() {
    log_info "生成性能报告..."
    
    local report_file="$LOG_DIR/performance_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "PostgreSQL性能报告 - $(date)"
        echo "======================================"
        echo ""
        
        echo "数据库基本信息:"
        execute_query "SELECT version();"
        echo ""
        
        echo "数据库统计信息:"
        execute_query "
            SELECT 
                datname as database,
                numbackends as connections,
                xact_commit as commits,
                xact_rollback as rollbacks,
                blks_read as disk_reads,
                blks_hit as cache_hits,
                tup_returned as rows_returned,
                tup_fetched as rows_fetched,
                tup_inserted as rows_inserted,
                tup_updated as rows_updated,
                tup_deleted as rows_deleted
            FROM pg_stat_database 
            WHERE datname = '$DB_NAME';
        "
        echo ""
        
        echo "表统计信息:"
        execute_query "
            SELECT 
                schemaname,
                tablename,
                n_tup_ins as inserts,
                n_tup_upd as updates,
                n_tup_del as deletes,
                n_live_tup as live_rows,
                n_dead_tup as dead_rows
            FROM pg_stat_user_tables 
            ORDER BY n_live_tup DESC;
        "
        echo ""
        
        echo "索引使用情况:"
        execute_query "
            SELECT 
                schemaname,
                tablename,
                indexname,
                idx_tup_read as index_reads,
                idx_tup_fetch as index_fetches
            FROM pg_stat_user_indexes 
            ORDER BY idx_tup_read DESC 
            LIMIT 10;
        "
        
    } > "$report_file"
    
    log_info "性能报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始数据库监控检查..."
    
    # 检查依赖
    check_dependencies
    
    # 测试连接
    if ! test_connection; then
        exit 1
    fi
    
    # 执行各项监控检查
    monitor_connections
    monitor_slow_queries
    monitor_cache_hit_ratio
    monitor_database_size
    monitor_locks
    
    # 生成性能报告（可选，通过参数控制）
    if [[ "$1" == "--report" ]]; then
        generate_performance_report
    fi
    
    log_info "数据库监控检查完成"
}

# 使用说明
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    echo "PostgreSQL数据库监控脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help, -h     显示此帮助信息"
    echo "  --report       生成详细的性能报告"
    echo ""
    echo "示例:"
    echo "  $0                # 执行基本监控检查"
    echo "  $0 --report       # 执行监控检查并生成性能报告"
    echo ""
    exit 0
fi

# 执行主函数
main "$@"