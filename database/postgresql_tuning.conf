# PostgreSQL性能优化配置文件
# 此文件包含针对项目管理系统的PostgreSQL性能优化参数
# 使用方法：将这些参数添加到postgresql.conf文件中，或通过ALTER SYSTEM命令设置

# =============================================================================
# 内存配置
# =============================================================================

# 共享缓冲区 - 建议设置为系统内存的25%
# 对于8GB系统，设置为2GB
shared_buffers = 2GB

# 工作内存 - 每个查询操作可使用的内存
# 计算公式：(总内存 - shared_buffers) / max_connections / 2
work_mem = 64MB

# 维护工作内存 - 用于维护操作（如VACUUM、CREATE INDEX）
maintenance_work_mem = 512MB

# 有效缓存大小 - 告诉优化器系统可用的缓存大小
effective_cache_size = 6GB

# =============================================================================
# 连接配置
# =============================================================================

# 最大连接数
max_connections = 200

# 超级用户保留连接数
superuser_reserved_connections = 3

# 连接超时时间（毫秒）
authentication_timeout = 60s

# 空闲连接超时时间
idle_in_transaction_session_timeout = 300s

# =============================================================================
# 查询优化配置
# =============================================================================

# 随机页面成本 - SSD存储建议设置较低值
random_page_cost = 1.1

# 顺序页面成本
seq_page_cost = 1.0

# CPU元组成本
cpu_tuple_cost = 0.01

# CPU索引元组成本
cpu_index_tuple_cost = 0.005

# CPU操作符成本
cpu_operator_cost = 0.0025

# 并行工作进程数
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
max_parallel_maintenance_workers = 4

# =============================================================================
# WAL（Write-Ahead Logging）配置
# =============================================================================

# WAL缓冲区大小
wal_buffers = 64MB

# 检查点完成目标时间比例
checkpoint_completion_target = 0.9

# 最大WAL大小
max_wal_size = 4GB

# 最小WAL大小
min_wal_size = 1GB

# WAL写入器延迟
wal_writer_delay = 200ms

# 提交延迟（用于批量提交优化）
commit_delay = 0
commit_siblings = 5

# =============================================================================
# 自动清理配置
# =============================================================================

# 启用自动清理
autovacuum = on

# 自动清理最大工作进程数
autovacuum_max_workers = 3

# 自动清理延迟
autovacuum_naptime = 60s

# 自动清理阈值
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50

# 自动清理比例因子
autovacuum_vacuum_scale_factor = 0.1
autovacuum_analyze_scale_factor = 0.05

# 自动清理成本延迟
autovacuum_vacuum_cost_delay = 10ms
autovacuum_vacuum_cost_limit = 2000

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别
log_min_messages = warning
log_min_error_statement = error

# 慢查询日志
log_min_duration_statement = 1000  # 记录执行时间超过1秒的查询

# 日志内容配置
log_checkpoints = on
log_connections = off
log_disconnections = off
log_lock_waits = on
log_statement = 'none'
log_temp_files = 10MB

# 日志格式
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_timezone = 'Asia/Shanghai'

# =============================================================================
# 统计信息配置
# =============================================================================

# 启用统计信息收集
track_activities = on
track_counts = on
track_io_timing = on
track_functions = pl

# 统计信息目标
default_statistics_target = 100

# =============================================================================
# 其他优化配置
# =============================================================================

# 启用JIT编译（PostgreSQL 11+）
jit = on
jit_above_cost = 100000
jit_inline_above_cost = 500000
jit_optimize_above_cost = 500000

# 时区设置
timezone = 'Asia/Shanghai'

# 语言环境
lc_messages = 'en_US.UTF-8'
lc_monetary = 'en_US.UTF-8'
lc_numeric = 'en_US.UTF-8'
lc_time = 'en_US.UTF-8'

# 默认文本搜索配置
default_text_search_config = 'pg_catalog.english'

# =============================================================================
# 应用程序特定优化
# =============================================================================

# 针对项目管理系统的特定优化

# 由于系统主要进行OLTP操作，优化小事务性能
fsync = on
synchronous_commit = on
full_page_writes = on

# 针对文档存储和搜索的优化
# 增加文本搜索相关的内存配置
shared_preload_libraries = 'pg_stat_statements'

# 启用查询统计扩展
pg_stat_statements.max = 10000
pg_stat_statements.track = all
pg_stat_statements.track_utility = off
pg_stat_statements.save = on

# =============================================================================
# 安全配置
# =============================================================================

# SSL配置（生产环境建议启用）
# ssl = on
# ssl_cert_file = 'server.crt'
# ssl_key_file = 'server.key'
# ssl_ca_file = 'ca.crt'

# 密码加密
password_encryption = scram-sha-256

# 行级安全
row_security = on

# =============================================================================
# 监控和维护
# =============================================================================

# 启用扩展统计信息
compute_query_id = on

# 死锁超时时间
deadlock_timeout = 1s

# 语句超时时间
statement_timeout = 300s

# 锁超时时间
lock_timeout = 30s

# =============================================================================
# 应用配置说明
# =============================================================================

# 1. 将此配置文件的内容添加到PostgreSQL的postgresql.conf文件中
# 2. 或者使用ALTER SYSTEM命令逐个设置参数：
#    ALTER SYSTEM SET shared_buffers = '2GB';
#    SELECT pg_reload_conf();
# 3. 重启PostgreSQL服务以使配置生效
# 4. 使用以下查询检查配置是否生效：
#    SELECT name, setting, unit FROM pg_settings WHERE name IN (
#        'shared_buffers', 'work_mem', 'maintenance_work_mem', 
#        'effective_cache_size', 'max_connections'
#    );

# 注意事项：
# - 这些配置基于8GB内存的系统，请根据实际硬件配置调整
# - 在生产环境中应用前，请先在测试环境中验证
# - 定期监控数据库性能，根据实际使用情况调整参数
# - 某些参数修改后需要重启PostgreSQL服务